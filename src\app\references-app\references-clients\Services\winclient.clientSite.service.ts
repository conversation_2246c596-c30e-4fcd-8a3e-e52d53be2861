import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment as env } from 'src/environments/environment';
import { ClientSite, ClientSiteFilter } from '../models/clientSite';
import { Page } from '../../referential/models/Page/page.model';
@Injectable({
  providedIn: 'root'
})
export class ClientSiteService {

  constructor(private http: HttpClient) { }
  
  searchClientSite(clientSiteCriteria: Partial<ClientSiteFilter>,params:Record<string,any> = {}) {
    return this.http.post<Page<ClientSite>>(`${env.winclient_base_url}/api/winclient/clients-sites/search`, clientSiteCriteria,{
      params:{
        ...params
      }
    });
  }


  getAllClientSitePaginated({page =0}:{page:number}) {
    return this.http.get<Page<ClientSite>>(`${env.winclient_base_url}/api/winclient/clients-sites`, { 
        params:{page}
     });
  }


  getClientSiteById(id: number) {
    return this.http.get<ClientSite>(`${env.winclient_base_url}/api/winclient/clients-sites/${id}`);
  }

  createClientSite(clientSite: ClientSite) {
    return this.http.post<ClientSite>(`${env.winclient_base_url}/api/winclient/clients-sites`, clientSite);
  }


  updateClientSite(id:string,clientSite: ClientSite) {
    return this.http.put<ClientSite>(`${env.winclient_base_url}/api/winclient/clients-sites/${id}`, clientSite);
  }
  
  deleteClientSite(id: string) {
    return this.http.delete<ClientSite>(`${env.winclient_base_url}/api/winclient/clients-sites/${id}`);
  }
 
}