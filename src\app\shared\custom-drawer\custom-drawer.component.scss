.custom-drawer-container {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  transition: all 0.3s;
  background: rgba(0, 0, 0, 0.4);
}

.custom-drawer-content {
  height: 100%;
  position: fixed;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;

  @media (max-width: 768px) {
    width: 95% !important;
  }
}

@keyframes shake {
  0% { transform: translateX(0); }
  10% { transform: translateX(-5px); }
  20% { transform: translateX(5px); }
  30% { transform: translateX(-5px); }
  40% { transform: translateX(5px); }
  50% { transform: translateX(-5px); }
  60% { transform: translateX(5px); }
  70% { transform: translateX(-5px); }
  80% { transform: translateX(5px); }
  90% { transform: translateX(-5px); }
  100% { transform: translateX(0); }
}

.shake-effect {
  animation: shake 1s ease-in-out;
  animation-iteration-count: 1;
}
