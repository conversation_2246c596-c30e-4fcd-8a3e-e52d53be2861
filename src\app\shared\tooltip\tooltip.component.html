<div 
  class="tooltip"
  [class]="'tooltip--' + theme + ' ' + className"
  [class.tooltip--animated]="animation !== 'none'"
  [class.tooltip--visible]="true"
  [attr.data-animation]="animation"
  [style.max-width]="maxWidth"
  role="tooltip"
  [attr.aria-hidden]="false">
  
  <div class="tooltip-content">
    <ng-container *ngIf="template; else textContent">
      <ng-container *ngTemplateOutlet="template; context: context"></ng-container>
    </ng-container>
    <ng-template #textContent>
      {{ content }}
    </ng-template>
  </div>
  
  <div class="tooltip-arrow" *ngIf="showArrow"></div>
</div>
