import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ProduitService } from '../../../Services/produit/produit.service';
import { Pagination } from 'src/app/references-app/referential/models/pagination.interface';
import { DataResult, SortDescriptor } from '@progress/kendo-data-query';
import { DiffPrixWinplusAndGroupe, UpdatePrixWinplus } from '../../../models/produit/ref-produits/diffPrixWinPlus';
import { UserInputService } from 'src/app/shared/services/user-input.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { ProduitSiteCriteriaForm } from '../../../models/produit/ref-produits/ProduitSiteCriteria.model';
import { ProduitSite } from '../../../models/produit/ref-produits/produitSite.model';
import { ProduitWinplus } from '../../../models/produit/ref-produits/produitsWinplusDto.model';
import { RowClassArgs } from '@progress/kendo-angular-grid';
import { WinclientSitesService } from 'src/app/references-app/references-clients/Services/winclient.sites.service';

@Component({
  selector: 'app-diff-prix-winplus',
  templateUrl: './diff-prix-winplus.component.html',
  styleUrls: ['./diff-prix-winplus.component.scss']
})
export class DiffPrixWinplusComponent implements OnInit {

  diffPrixWinplusAndGroupe: DataResult = {
    data: [],
    total: 0
  };
  navigation : Pagination = {
    pageSize :21,
    skip:0,
    sortField: '',
    sortMethod: '',
  };

  linkedProductSiteNavigation: Pagination = {
    pageSize :21,
    skip:0,
    sortField: '',
    sortMethod: '',
  };

  produitGroupe: ProduitSite[] = [];
  produitWinplus: ProduitWinplus[] = [];
  produitSiteLinkedToGroupe: DataResult = {
    data: [],
    total: 0
  };

  sort: SortDescriptor[] = [];
  productSiteLinkedToGroupeSort: SortDescriptor[] = [];

  // Filter properties
  isFilterDrawerOpen = false;
  showTranscoDrawer = false;
  isTranscoGroupeLoading = false;
  isTranscoWinplusLoading = false;
  isLinkedProductSiteLoading = false;
  filterForm: FormGroup;
  filterCriteria: Partial<DiffPrixWinplusAndGroupe> = {};
  clickedItem: DiffPrixWinplusAndGroupe = null;
  sites : any[] = [];


  constructor(
    private produitService: ProduitService,
    private alertService: AlertService,
    private userInputService: UserInputService,
    private fb: FormBuilder
    ) {
    this.initFilterForm();
  }

  ngOnInit() {
    this.getDeffPrixWinplusAndGroupe();
    this.loadSites();
  }

  loadSites(){
    this.produitService.sites$.subscribe(sites => {
      this.sites = sites;
    })
  }

  private initFilterForm() {
    this.filterForm = this.fb.group({
      codeWinplus: [''],
      codeGroupe: [''],
      designationWinplus: [''],
      designationGroupe: [''],
    });
  }

  getDeffPrixWinplusAndGroupe() {
    this.produitService.getDiffPrixWinplusAndGroupe(this.navigation, this.filterCriteria).subscribe({
      next: (res) => {
        this.diffPrixWinplusAndGroupe.data = res.content;
        this.diffPrixWinplusAndGroupe.total = res.totalElements;
      },
    })
  }

  pageChange(skip: number) {
    this.navigation.skip = skip;
    this.getDeffPrixWinplusAndGroupe();
  }


  updatePrixWinplus(dataItem: DiffPrixWinplusAndGroupe) {

    this.userInputService.confirm("Confirmer la mise à jour du prix Winplus ?", "Voulez-vous vraiment mettre à jour le prix Winplus ?","Oui","Non").then((res) => {
      if(res){
        const updatePrixWinplus = new UpdatePrixWinplus({
          codeWinplus: dataItem.codeWinplus,
          codeGroupe: dataItem.codeGroupe,
        });
        this.produitService.updatePrixWinplus(updatePrixWinplus).subscribe({
          next: (res) => {
            this.alertService.success("Prix Winplus mis à jour avec succès", "Prix Winplus");
            this.getDeffPrixWinplusAndGroupe();
          },
        });
      }
    }).catch(()=>{})
    
  }


   sortChange(sort: SortDescriptor[]) {
    this.sort = sort;
    if (this.sort.length > 0) {
      this.navigation.sortField = this.sort[0].field;
      this.navigation.sortMethod = this.sort[0].dir;
    } else {
      this.navigation.sortField = '';
      this.navigation.sortMethod = '';
    }
    this.getDeffPrixWinplusAndGroupe();
  }

  // Filter methods
  openFilterDrawer() {
    this.isFilterDrawerOpen = true;
    setTimeout(() => {
      (document.querySelector('#codeWinplus') as HTMLInputElement)?.focus();
    }, 200);
  }
  closeTranscoDrawerAndReset() {
    this.showTranscoDrawer = false;
    this.clickedItem = null;
    this.produitGroupe = [];
    this.produitWinplus = [];
    this.produitSiteLinkedToGroupe.data = [];
    this.produitSiteLinkedToGroupe.total = 0;
  }

  onCellClick(event: any) {
    console.log('event', event)
    if (event.column.field !== 'codeGroupe' && event.column.field !== 'codeWinplus' && event.column.title !== 'Action') {
      const dataItem = event.dataItem as DiffPrixWinplusAndGroupe;
      this.openTranscoDrawer(dataItem);
    }
  }

  openTranscoDrawer(dataItem: DiffPrixWinplusAndGroupe) {
    this.clearClickedItem();
    this.clickedItem = dataItem;
    this.showTranscoDrawer = true;
    this.clickedItem['isClicked'] = true;
    this.getProduitGroupe();
  }

  private clearClickedItem() {
    this.diffPrixWinplusAndGroupe.data.forEach(item => {
      delete item['isClicked'];
    });
  }

  onFilterSubmit() {
    const formValue = this.filterForm.value;
    this.filterCriteria = {};

    // Only add non-empty values to filter criteria
    if (formValue.codeWinplus && formValue.codeWinplus.trim()) {
      this.filterCriteria.codeWinplus = formValue.codeWinplus.trim();
    }
    if (formValue.codeGroupe && formValue.codeGroupe.trim()) {
      this.filterCriteria.codeGroupe = formValue.codeGroupe.trim();
    }
    if (formValue.designationWinplus && formValue.designationWinplus.trim()) {
      this.filterCriteria.designationWinplus = formValue.designationWinplus.trim();
    }
    if (formValue.designationGroupe && formValue.designationGroupe.trim()) {
      this.filterCriteria.designationGroupe = formValue.designationGroupe.trim();
    }

    // Reset pagination and reload data
    this.navigation.skip = 0;
    this.getDeffPrixWinplusAndGroupe();
    this.isFilterDrawerOpen = false;
  }

  resetFilter() {
    this.filterForm.reset();
    this.filterCriteria = {};
    this.navigation.skip = 0;
    this.getDeffPrixWinplusAndGroupe();
    this.isFilterDrawerOpen = false;
  }


  private getProduitGroupe(){
    this.isTranscoGroupeLoading = true;
    const produitCriteria = new ProduitSiteCriteriaForm({
      codeSite:[0],
      codeGroupe: this.clickedItem?.codeGroupe,
    })
    this.produitService.getProduitsSiteByCriteria(this.navigation, produitCriteria).subscribe({
      next: (res) => {
        this.produitGroupe = res.content;
      },
      complete: () => {
        this.isTranscoGroupeLoading = false;
        this.getProduitWinplus();
      }
    })
  }

  getProduitWinplus(){
    this.isTranscoWinplusLoading = true;
    this.produitService.getProduitsWinplusByCriteria({pageSize: 21,skip: 0}, {
      codeWinplus: this.clickedItem?.codeWinplus,
    }).subscribe({
      next: (res) => {
        this.produitWinplus = res.content;
        this.getProduitSiteLinkedToGroupe();
      },
      complete: () => {
        this.isTranscoWinplusLoading = false;
      }
    })
  }

  getProduitSiteLinkedToGroupe(){
    const produitCriteria = new ProduitSiteCriteriaForm({
      codeGroupe: this.clickedItem?.codeGroupe,
      codeSite: this.sites.map(site => site.codeSite).filter(code => code !== 0),
    })
    this.isLinkedProductSiteLoading = true;
    this.produitService.getProduitsSiteByCriteria(this.linkedProductSiteNavigation, produitCriteria).subscribe({
      next: (res) => {
        this.produitSiteLinkedToGroupe.data = res.content;
        this.produitSiteLinkedToGroupe.total = res.totalElements;
      },
      complete: () => {
        this.isLinkedProductSiteLoading = false;
      }
    })
  }

  onSortChangeProductSiteLinkedToGroupe(sort: SortDescriptor[]) {
    this.linkedProductSiteNavigation.sortField = sort[0]?.field || '';
    this.linkedProductSiteNavigation.sortMethod = sort[0]?.dir || '';
    this.productSiteLinkedToGroupeSort = sort;
    this.getProduitSiteLinkedToGroupe();
  }

  linkedProductSitePageChange(skip: number) {
    this.linkedProductSiteNavigation.skip = skip;
    this.getProduitSiteLinkedToGroupe();
  }

  rowClass(args: RowClassArgs) {
    if (args.dataItem?.isClicked) {
      return { 'highlight-row-clicked': true };
    }
    return '';
  }

}
