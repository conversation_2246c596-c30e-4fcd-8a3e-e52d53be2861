<!-- start page title -->
<div class="row rowline mb-3">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h1 class="page-title ps-2">Exécution Batch Admin</h1>
      <div class="d-flex flex-wrap justify-content-end">
        <!-- Additional actions can go here -->
      </div>
    </div>
  </div>
</div>

<div class="custom-container mx-auto">
  <div class="row mb-3">
    <div class="col-12">
      <h2 class="fs-3-5 mb-0">Liste des batches disponibles</h2>
      <p class="text-muted mt-1 mb-0 small">Gérez et exécutez vos processus batch</p>
    </div>
  </div>

  <!-- Batches Grid -->
  <div class="row k-gap-y-1 g-1 g-lg-1">
    <!-- Batch Card -->
    <div class="col-12 col-sm-6 col-lg-4 col-xxl-3" *ngFor="let batch of batches;">
      <article class="card h-100 shadow-sm ">

        <!-- Card Header -->
        <header class="card-header">
          <div class="d-flex align-items-center">
            <div class="border-start flex-shrink-0"></div>
            <div class="flex-grow-1 min-w-0">
              <h3 class="h6 mb-0 text-truncate" [title]="batch.title">
                {{ batch.title }}
              </h3>
            </div>
          </div>
        </header>

        <!-- Card Body -->
        <div class="card-body d-flex flex-column">
          <!-- Description -->
          <p class="text-muted flex-grow-1">
            {{ batch.description || batch.title }}
          </p>

          <!-- Status Section -->
          <div class="mt-auto">
            <div class="d-flex align-items-center justify-content-between mb-2">
              <span class="badge bg-secondary bg-opacity-10 text-secondary">
                Statut
              </span>
              <app-element-status [objectStatut]="getObject(batch)"></app-element-status>
            </div>

            <!-- Last Execution -->
            <div class="text-muted small" *ngIf="batch['lastExec']">
              <i class="bi bi-clock-history me-1"></i>
              <time [dateTime]="batch['lastExec']">
                {{ batch['lastExec'] | date : 'dd/MM/YYYY à HH:mm' }}
              </time>
            </div>
          </div>
        </div>

        <!-- Card Footer -->
        <footer class="card-footer">
          <div class="d-flex justify-content-between align-items-center gap-2">
            <!-- History Button -->
            <button 
              type="button"
              class="btn btn-historique flex-fill flex-sm-grow-0" 
              (click)="open(timelineModal)" 
              [disabled]="true"
              [attr.aria-label]="'Voir l\'historique de ' + batch.title">
              <i class="bi bi-archive" aria-hidden="true"></i>
              <span class="d-none d-sm-inline">Historique</span>
              <span class="d-sm-none">Hist.</span>
            </button>

            <!-- Execute Button -->
            <button 
              type="button"
              class="btn btn-execute flex-fill flex-sm-grow-0" 
              (click)="openForm(batch, batchFormModal)" 
              [attr.aria-label]="'Exécuter ' + batch.title">
              <i class="mdi mdi-play-circle" aria-hidden="true"></i>
              <span>Exécuter</span>
            </button>
          </div>
        </footer>

      </article>
    </div>
  </div>
</div>

<!-- Modal -->

<ng-template #timelineModal let-cf="close" let-df="dismiss">
  <div class="modal-header">
    <span class="vertical-line"></span>
    <h5 class="modal-title card-title">Historique des batches</h5>
    <button type="button" class="btn-close" (click)="cf('close')"></button>
  </div>
  <div id="content">
    <ul class="timeline">
      <li class="event  d-sm-flex align-items-sm-center justify-content-sm-between" *ngFor="let item of timelineItems"
        [attr.data-date]="item.date">
        <p>{{ item.description }}</p>
        <span [ngClass]="item.statusClass" class="status-pill">{{ item.status }}</span>
      </li>


    </ul>
  </div>
</ng-template>


<ng-template #batchFormModal let-modal let-df="dismiss">
  <div class="modal-header">

    <h4 class="modal-title card-title">Exécuter : {{selectedBatch.title}}</h4>
    <button type="button" class="btn-close"></button>
  </div>
  <div id="modal-body p-2">
    <div class="form-group p-2 m-2">
      <form [formGroup]="batchForm" (ngSubmit)="submitForm()">
        <div *ngFor="let field of selectedBatch?.configuration">
          <div class="mb-3" [ngSwitch]="field.type">
            <!-- Text Input -->
            <div *ngSwitchCase="'text'">
              <label for="{{field.name}}" class="form-label">{{ field.label }}</label>
              <input type="text" class="form-control" id="{{field.name}}" formControlName="{{field.name}}">
            </div>
            <!-- Number Input -->
            <div *ngSwitchCase="'number'">
              <label for="{{field.name}}" class="form-label">{{ field.label }}</label>
              <input type="number" class="form-control text-end" id="{{field.name}}" formControlName="{{field.name}}">
            </div>


            <!-- Select Input -->
            <div *ngSwitchCase="'select'">
              <label for="{{field.name}}" class="form-label">{{ field.label }}</label>
              <select class="form-select" id="{{field.name}}" formControlName="{{field.name}}">
                <option [ngValue]="null"></option>

                <option *ngFor="let option of field.options" [value]="option.value">{{ option.label }}</option>
              </select>
            </div>

            <!-- Checkbox Input -->
            <div *ngSwitchCase="'checkbox'">
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="{{field.name}}" formControlName="{{field.name}}">
                <label class="form-check-label" for="{{field.name}}">
                  {{ field.label }}
                </label>
              </div>
            </div>
            <div *ngSwitchCase="'date'">
              <label [for]="field.name" class="form-label-cus">{{field.label}}</label>
              <app-date-picker [formControlName]="field.name"></app-date-picker>

            </div>
          </div>
        </div>


      </form>
    </div>


  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss('close')">Quitter</button>
    <button type="submit" class="btn btn-warning" [disabled]="batchForm.invalid"
      (click)="modal.close();submitForm()">Exécuter</button>
  </div>
</ng-template>