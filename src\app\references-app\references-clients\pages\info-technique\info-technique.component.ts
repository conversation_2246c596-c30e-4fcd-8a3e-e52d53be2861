import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { WinclientInfoTechniqueService } from '../../Services/winclient.infoTechnique.service';
import { Pagination } from 'src/app/references-app/referential/models/pagination.interface';
import { InfoTechniqueCriteria, TypeInternet, TechniqueInfo, OsVersion, WinpharmVersion } from '../../models/infoTechnique.model';
import { DataResult, SortDescriptor } from '@progress/kendo-data-query';
import { AlertService } from 'src/app/shared/services/alert.service';
import { RowClassArgs } from '@progress/kendo-angular-grid';
import { AuthService } from 'src/app/shared/services/auth.service';

@Component({
  selector: 'app-info-technique',
  templateUrl: './info-technique.component.html',
  styleUrls: ['./info-technique.component.scss']
})
export class InfoTechniqueComponent implements OnInit {

  techniqueInfoCriteria: Partial<InfoTechniqueCriteria> =  {};
  sort : SortDescriptor[] = [];
  infoTechniques : DataResult = {
    data: [],
    total: 0
  };
  navigation: Pagination = {
    skip: 0,
    pageSize: 25,
    sortField: '',
    sortMethod: '',
  };

  // Filter drawer properties
  isFilterDrawerOpen = false;
  filterForm: FormGroup;

  // Edit modal properties
  editForm: FormGroup;
  selectedItem: TechniqueInfo | null = null;
  modalRef: NgbModalRef | null = null;
  @ViewChild('editModal', { static: false }) editModal!: TemplateRef<any>;

  typeInternetOptions = [
    { value: TypeInternet.FIBRE, label: 'FIBRE' },
    { value: TypeInternet.ADSL, label: 'ADSL' },
    { value: TypeInternet.SANS, label: 'SANS' }
  ];

  osVersionOptions = [
    { value: OsVersion.WINDOWS_7, label: 'Windows 7' },
    { value: OsVersion.WINDOWS_8, label: 'Windows 8' },
    { value: OsVersion.WINDOWS_10, label: 'Windows 10' },
    { value: OsVersion.WINDOWS_11, label: 'Windows 11' },
    { value: OsVersion.WINDOWS_SERVER_2012, label: 'Windows Server 2012' },
    { value: OsVersion.WINDOWS_SERVER_2016, label: 'Windows Server 2016' },
    { value: OsVersion.WINDOWS_SERVER_2019, label: 'Windows Server 2019' },
    { value: OsVersion.AUTRE, label: 'Autre' }
  ];

  winpharmVersionOptions = [
    { value: WinpharmVersion.V4_6, label: '4.6' },
    { value: WinpharmVersion.V6_2, label: '6.2' },
    { value: WinpharmVersion.V7_1, label: '7.1' },
    { value: WinpharmVersion.V8, label: '8' },
    { value: WinpharmVersion.V9, label: '9' },
    { value: WinpharmVersion.V10_04, label: '10.04' },
    { value: WinpharmVersion.V10_2, label: '10.2' },
    { value: WinpharmVersion.V11_41, label: '11.41' },
    { value: WinpharmVersion.V12_3, label: '12.3' },
    { value: WinpharmVersion.V13_1, label: '13.1' },
    { value: WinpharmVersion.V13_31, label: '13.31' },
    { value: WinpharmVersion.V13_32, label: '13.32' },
    { value: WinpharmVersion.V13_34, label: '13.34' },
    { value: WinpharmVersion.V13_35, label: '13.35' },
    { value: WinpharmVersion.V14, label: '14' },
    { value: WinpharmVersion.V15_1, label: '15.1' },
    { value: WinpharmVersion.V15_2, label: '15.2' },
    { value: WinpharmVersion.V15_367, label: '15.367' },
    { value: WinpharmVersion.V16_0, label: '16.0' },
    { value: WinpharmVersion.V16_1, label: '16.1' },
    { value: WinpharmVersion.V16_2, label: '16.2' },
    { value: WinpharmVersion.V17_1_1, label: '17.1.1' },
    { value: WinpharmVersion.V17_2, label: '17.2' },
    { value: WinpharmVersion.V18, label: '18' },
    { value: WinpharmVersion.V18_1, label: '18.1' },
    { value: WinpharmVersion.V18_2, label: '18.2' }
  ]

  constructor(
    private infoTechniqueService: WinclientInfoTechniqueService,
    private fb: FormBuilder,
    private modalService: NgbModal,
    private alertService: AlertService,
    private authService: AuthService
  ) {
    this.initializeFilterForm();
    this.initializeEditForm();
  }

  ngOnInit() {
    if(this.authService.hasAnyAuthority(['ROLE_SUPERADMIN'])){
      this.getInfoTechnique();
    }
    this.winpharmVersionOptions = this.winpharmVersionOptions.sort((a, b) => {
      return this.sortWinpharmVersions([a.value, b.value])[0] === a.value ?  1: -1;
    });
  }


  sortWinpharmVersions(versions:string[]){
    return versions.sort((a, b) => {
      const partsA = a.replace('V', '').split('_').join('.').split('.');
      const partsB = b.replace('V', '').split('_').join('.').split('.');
      
      for (let i = 0; i < Math.max(partsA.length, partsB.length); i++) {
        const numA = parseInt(partsA[i] || '0');
        const numB = parseInt(partsB[i] || '0');
        if (numA !== numB) {
          return numA - numB;
        }
      }
      return 0;
    });
  }

  initializeFilterForm() {
    this.filterForm = this.fb.group({
      cliCode: [''],
      transcoWinplus: [null],
      raisonSociale: [null],
      nomPharmacien: [null],
      typeInternet: [null],
      versionOs: [null],
      versionWinpharm: [null]
    });
  }

  initializeEditForm() {
    this.editForm = this.fb.group({
      versionWinpharm: [null],
      typeInternet: [''],
      versionOs: [null],
      transcoWinplus: [null],
      gsmResponsable: ['']
    });
  }

  getInfoTechnique() {
    const cleanedCriteria = this.cleanEmptyValues(this.techniqueInfoCriteria);
    this.infoTechniqueService.searchInfoTechnique(this.navigation, cleanedCriteria).subscribe({
      next: (res) => {
        this.infoTechniques.data = res.content;
        this.infoTechniques.total = res.totalElements;
      }
    });
  }


  cleanEmptyValues<T>(filterValues: T): T {
    const cleanedValues = { ...filterValues };
    Object.keys(cleanedValues).forEach(key => {
      if (cleanedValues[key] === null || cleanedValues[key] === undefined || typeof cleanedValues[key] === 'string' && cleanedValues[key].trim() === '') {
        delete cleanedValues[key];
      }
    });
    return cleanedValues;
  }


  sortChange(sort: SortDescriptor[]) {
    this.sort = sort;
    this.navigation.sortField = sort[0]?.field;
    this.navigation.sortMethod = sort[0]?.dir;
    if(this.authService.hasAnyAuthority(['ROLE_SUPERADMIN'])){
    this.getInfoTechnique();
    }
  }

  pageChange(skip: number) {
    this.navigation.skip = skip;
    this.getInfoTechnique();
  }

  // Filter drawer methods
  toggleFilterDrawer() {
    this.isFilterDrawerOpen = !this.isFilterDrawerOpen;
  }

  onFilterSubmit() {
    if (this.filterForm.valid) {
      const formValues = this.filterForm.value;
      const techniqueCriteria = new InfoTechniqueCriteria({
        cliCode: formValues.cliCode,
        transcoWinplus: formValues.transcoWinplus,
        raisonSociale: formValues.raisonSociale,
        nomPharmacien: formValues.nomPharmacien,
        typeInternet: formValues.typeInternet,
        versionOs: formValues.versionOs,
        versionWinpharm: formValues.versionWinpharm
      });
      this.techniqueInfoCriteria = this.cleanEmptyValues(techniqueCriteria);
      
      if(!this.haveActiveFilters()){
        this.alertService.error("Veuillez sélectionner au moins un filtre");
        return;
      }
      this.navigation.skip = 0; // Reset to first page
      this.getInfoTechnique();
      this.isFilterDrawerOpen = false;
    }
  }

  haveActiveFilters(): boolean {
    return Object.keys(this.techniqueInfoCriteria).some(key => {
      const value = this.techniqueInfoCriteria[key];
      return value !== null && value !== undefined && value !== '';
    });
  }

  resetFilters() {
    this.filterForm.reset();
    this.techniqueInfoCriteria = {};
    this.navigation.skip = 0;
    this.isFilterDrawerOpen = false;
    if(this.authService.hasAnyAuthority(['ROLE_SUPERADMIN'])){
      this.getInfoTechnique();
    }else{
      this.infoTechniques.data = [];
      this.infoTechniques.total = 0;
    }
  }

  // Edit modal methods
  openEditDrawer(item: TechniqueInfo) {
    this.selectedItem = item;
    this.clearClickedItem();
    item['isClicked'] = true;
    this.editForm.patchValue({
      versionWinpharm: item.versionWinpharm,
      typeInternet: item.typeInternet,
      versionOs: item.versionOs,
      transcoWinplus: item.transcoWinplus,
      gsmResponsable: item.gsmResponsable
    });
    this.modalRef = this.modalService.open(this.editModal, { size: 'lg' });
  }

  onEditSubmit() {
    if (this.editForm.valid && this.selectedItem) {
      const formValues = this.editForm.value;
      const updatedItem = new TechniqueInfo({
        ...this.selectedItem,
        versionWinpharm: formValues.versionWinpharm,
        typeInternet: formValues.typeInternet,
        versionOs: formValues.versionOs,
        transcoWinplus: formValues.transcoWinplus,
        gsmResponsable: formValues.gsmResponsable
      });

      this.infoTechniqueService.createOrUpdateInfoTechnique(updatedItem).subscribe({
        next: (response) => {
          this.updateLigne({...response,isClicked : true});
          this.modalRef?.close();
          this.alertService.success('Info technique mise à jour avec succès');
        }
      });
    }
  }

  private updateLigne(item: TechniqueInfo) {
    const index = this.infoTechniques.data.findIndex(i => i.cliCode === item.cliCode);
    if (index !== -1) {
      this.infoTechniques.data[index] = item;
    }
  }

  closeModal() {
    this.modalRef?.close();
  }

  // Helper method to get OS version label
  getOsVersionLabel(osVersion: OsVersion): string {
    const option = this.osVersionOptions.find(opt => opt.value === osVersion);
    return option ? option.label : osVersion;
  }

  // Helper method to get Winpharm version label
  getWinpharmVersionLabel(winpharmVersion: WinpharmVersion): string {
    const option = this.winpharmVersionOptions.find(opt => opt.value === winpharmVersion);
    return option ? option.label : winpharmVersion;
  }

  // Compare function for select dropdowns
  compareFn(option1: any, option2: any): boolean {
    return option1 && option2 ? option1 === option2 : option1 === option2;
  }


  clearClickedItem(){
    this.infoTechniques.data.forEach(item => {
      delete item['isClicked'];
    });
  }

  rowClass(args: RowClassArgs) {
    if (args.dataItem?.isClicked) {
      return { 'highlight-row-clicked': true };
    }
    return '';
  }

}
