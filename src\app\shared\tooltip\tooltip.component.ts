import { 
  Component, 
  Input, 
  TemplateRef, 
  ChangeDetectionStrategy, 
  ViewEncapsulation,
  OnInit,
  ElementRef,
  Renderer2 
} from '@angular/core';

@Component({
  selector: 'app-tooltip',
  templateUrl: './tooltip.component.html',
  styleUrls: ['./tooltip.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class TooltipComponent implements OnInit {
  @Input() content: string = '';
  @Input() template: TemplateRef<any> | null = null;
  @Input() context: any = {};
  @Input() maxWidth: string = '200px';
  @Input() theme: 'dark' | 'light' | 'custom' = 'dark';
  @Input() animation: 'fade' | 'scale' | 'slide' | 'none' = 'fade';
  @Input() showArrow: boolean = true;
  @Input() className: string = '';

  constructor(
    private elementRef: ElementRef,
    private renderer: Renderer2
  ) {}

  ngOnInit() {
    // Set initial positioning - the directive will handle final positioning
    const element = this.elementRef.nativeElement;
    this.renderer.setStyle(element, 'position', 'fixed');
    this.renderer.setStyle(element, 'top', '0');
    this.renderer.setStyle(element, 'left', '0');
  }
}