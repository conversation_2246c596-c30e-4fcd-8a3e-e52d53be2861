{"name": "winref", "version": "1.1.7", "scripts": {"ng": "ng", "start": "ng serve  --disable-host-check --host 0.0.0.0", "host": "ipconfig && ng serve --host 0.0.0.0 --port 8080", "deploy:prod": "nexus-publisher -p prod", "deploy:dev": "nexus-publisher -p dev ", "watch": "ng build --watch --configuration development", "doc": "npx compodoc -p tsconfig.doc.json", "test": "ng test", "sonar": "sonar-scanner"}, "private": true, "dependencies": {"@agm/core": "^3.0.0-beta.0", "@angular-slider/ngx-slider": "^2.0.3", "@angular/animations": "~12.0.1", "@angular/common": "~12.0.1", "@angular/compiler": "~12.0.1", "@angular/core": "~12.0.1", "@angular/forms": "~12.0.1", "@angular/platform-browser": "~12.0.1", "@angular/platform-browser-dynamic": "~12.0.1", "@angular/router": "~12.0.1", "@angular/service-worker": "^12.0.1", "@asymmetrik/ngx-leaflet": "^8.1.0", "@ckeditor/ckeditor5-angular": "^1.1.2", "@ckeditor/ckeditor5-build-balloon": "^15.0.0", "@ckeditor/ckeditor5-build-classic": "^15.0.0", "@floating-ui/dom": "^1.6.13", "@fullcalendar/angular": "^5.8.0", "@fullcalendar/bootstrap": "^5.8.0", "@fullcalendar/core": "^5.8.0", "@fullcalendar/daygrid": "^5.8.0", "@fullcalendar/interaction": "^5.8.0", "@fullcalendar/list": "^5.8.0", "@fullcalendar/timegrid": "^5.8.0", "@ng-bootstrap/ng-bootstrap": "^11.0.0-beta.1", "@progress/kendo-angular-buttons": "^5.0.0", "@progress/kendo-angular-common": "^1.0.0", "@progress/kendo-angular-dateinputs": "^4.0.0", "@progress/kendo-angular-dropdowns": "^4.0.0", "@progress/kendo-angular-excel-export": "^3.0.0", "@progress/kendo-angular-grid": "^4.8.4", "@progress/kendo-angular-inputs": "^6.0.0", "@progress/kendo-angular-intl": "^2.0.0", "@progress/kendo-angular-l10n": "^2.0.0", "@progress/kendo-angular-label": "^3.0.0", "@progress/kendo-angular-pager": "^2.0.1", "@progress/kendo-angular-pdf-export": "^2.0.0", "@progress/kendo-angular-popup": "^3.0.0", "@progress/kendo-angular-tooltip": "^2.1.6", "@progress/kendo-angular-treeview": "^5.0.0", "@progress/kendo-data-query": "^1.0.0", "@progress/kendo-drawing": "^1.0.0", "@progress/kendo-licensing": "^1.0.2", "@progress/kendo-theme-bootstrap": "^4.41.2", "@progress/kendo-theme-default": "^4.42.0", "@sentry/angular": "^7.1.1", "@sentry/tracing": "^7.1.1", "@sophatel/api-recorder": "0.0.3", "@stomp/ng2-stompjs": "^8.0.0", "@types/frappe-gantt": "^0.4.2", "@types/googlemaps": "3.39.13", "@types/pdfmake": "^0.1.21", "@types/quill": "1.3.10", "@types/sortablejs": "^1.10.7", "angular2-hotkeys": "^2.4.0", "apexcharts": "^3.27.1", "bcryptjs": "^2.4.3", "bootstrap": "^5.1.0", "chart.js": "^2.9.3", "cldr-data": "^36.0.1", "crypto-js": "^4.1.1", "dexie": "^4.0.8", "eta": "^2.0.1", "firebase": "^8.10.0", "frappe-gantt": "^0.5.0", "jsvectormap": "^1.3.2", "jwt-decode": "^3.1.2", "leaflet": "^1.9.4", "moment-timezone": "^0.5.46", "ng-apexcharts": "^1.5.12", "ng-click-outside": "^8.0.0", "ng-select2-component": "^7.3.1", "ng2-charts": "^2.3.0", "ng2-dragula": "^2.1.1", "ngx-cookie-service": "^12.0.3", "ngx-drag-drop": "^2.0.0", "ngx-dropzone": "^3.0.0", "ngx-mask": "^12.0.0", "ngx-quill": "^14.1.2", "ngx-simplemde": "^10.0.0", "ngx-sortablejs": "^11.1.0", "pdfmake": "^0.2.5", "quill": "^1.3.7", "rxjs": "^6.6.0", "simplebar-angular": "3.0.0-beta.4", "smooth-scrollbar": "^8.6.3", "sortablejs": "^1.14.0", "swiper": "^6.8.4", "tslib": "^2.1.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~12.0.1", "@angular/cli": "~12.0.1", "@angular/compiler-cli": "~12.0.1", "@angular/localize": "^12.0.5", "@sophatel/nexus-publisher": "^1.0.6", "@types/jasmine": "~3.6.0", "@types/node": "^12.20.36", "jasmine-core": "~3.7.0", "karma": "^6.3.6", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "sonar-scanner": "^3.1.0", "typescript": "~4.2.3"}, "browser": {"fs": false, "path": false, "os": false, "crypto": false}}