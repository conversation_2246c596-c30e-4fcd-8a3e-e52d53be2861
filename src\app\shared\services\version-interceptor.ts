import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>p<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { VersionService } from "./version.service";

 
@Injectable()
export class AppVersionInterceptor implements HttpInterceptor {
	constructor(
		private versionService: VersionService
	) { }
	intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
		req = req.clone({
			setHeaders: {
				"X-ClientVersion": this.versionService.getVersion()
			}
		});
		return next.handle(req);
	}



}