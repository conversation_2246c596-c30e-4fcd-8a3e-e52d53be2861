/* Modern Container Styles */
.custom-container {
  padding: 1.5rem;
  background:#fff;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .custom-container {
    padding: 1rem;
    margin: 0.5rem;
  }
}

/* Modern Card Grid */
.k-gap-y-2 {
  gap: 1rem;
}

@media (min-width: 1200px) {
  .k-gap-y-2 {
    gap: 1.25rem;
  }
}

/* Modern Card Styles */
.card {
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(224, 224, 224, 0.7), rgba(185, 185, 185, 0.8)) !important;
  border: 1px solid #d8d8d8 !important;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
  max-height: 280px;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8, #7c3aed);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.card:hover::before {
  opacity: 1;
}

/* Modern Card Header */
.card-header {
  padding: 1rem 1rem 0.5rem;
  background: transparent;
  border-bottom: none;
  position: relative;
}

.card-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #0f172a;
  margin: 0;
  line-height: 1.3;
}

.border-start {
  width: 3px;
  height: 1.5rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
  margin-right: 0.75rem;
}

/* Modern Card Body */
.card-body {
  padding: 0.5rem 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-body p {
  color: #475569;
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Status Section */
.badge {
  font-size: 0.7rem;
  font-weight: 600;
  letter-spacing: 0.025em;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.badge.bg-secondary {
  background-color: #334155 !important;
  color: #f8fafc !important;
}

.text-muted {
  color: #475569 !important;
  font-size: 0.7rem;
}

.small {
  font-size: 0.7rem;
  color: #475569;
}

/* Modern Card Footer */
.card-footer {
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
  margin-top: auto;
}

/* Modern Button Styles */
.btn {
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  position: relative;
  overflow: hidden;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  min-height: 32px;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.btn:hover::before {
  transform: translateX(0);
}

.btn-historique {
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  color: #334155;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  border: 1px solid #cbd5e1;
}

.btn-historique:hover:not(:disabled) {
  background: linear-gradient(135deg, #cbd5e1, #94a3b8);
  color: #1e293b;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

.btn-historique:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  background: #f1f5f9;
  color: #94a3b8;
}

.btn-execute {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
}

.btn-execute:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Responsive Button Layout */
@media (max-width: 576px) {
  .card-footer .d-flex {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .btn {
    width: 100%;
    font-size: 0.8rem;
  }
}

/* Page Title */
.page-title {
  font-size: 1rem;
  font-weight: 700;
  color: #0f172a;
  margin: 0;
}

.fs-3-5 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #0f172a;
  margin-bottom: 1rem;
}

/* Subtitle */
.text-muted.mt-1 {
  color: #475569 !important;
  font-size: 0.875rem;
}

/* Icon Styling */
.bi, .mdi {
  font-size: 0.875rem;
}

/* Timeline Styles - Keep existing timeline styles */
body {
  margin-top: 20px;
}
.timeline {
  border-left: 3px solid #e0e0e0;
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
  margin-left: 200px;
  max-width: 70%;
  letter-spacing: 0.2px;
  position: relative;
  line-height: 1.4em;
  font-size: 1.03em;
  padding: 50px;
  list-style: none;
  text-align: left;
}

@media (max-width: 767px) {
  .timeline {
    max-width: 98%;
    margin-left: 50px;
    padding: 25px;
  }
}

.timeline h1 {
  font-weight: 300;
  font-size: 1.4em;
}

.timeline h2,
.timeline h3 {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 10px;
}

.timeline .event {
  padding-bottom: 25px;
  margin-bottom: 25px;
  position: relative;
}

@media (max-width: 767px) {
  .timeline .event {
    padding-top: 30px;
  }
}

.timeline .event:last-of-type {
  padding-bottom: 0;
  margin-bottom: 0;
  border: none;
}

.timeline .event:before,
.timeline .event:after {
  position: absolute;
  display: block;
  top: 0;
}

.timeline .event:before {
  left: -207px;
  content: attr(data-date);
  text-align: right;
  font-weight: 100;
  font-size: 0.9em;
  min-width: 120px;
}

@media (max-width: 767px) {
  .timeline .event:before {
    left: 0px;
    text-align: left;
  }
}

.timeline .event:after {
  -webkit-box-shadow: 0 0 0 3px #3f94c1;
  box-shadow: 0 0 0 3px #3f94c1;
  left: -55.8px;
  background: #3f94c1;
  border-radius: 50%;
  height: 9px;
  width: 9px;
  content: "";
  top: 5px;
}

@media (max-width: 767px) {
  .timeline .event:after {
    left: -31.8px;
  }
}

.timeline {
  &:before {
    background-color: white !important;
  }
}
/* Status Pills - Enhanced */
.status-pill {
  display: inline-block;
  text-align: center;
  white-space: nowrap;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 700;
  min-width: 60px;
  letter-spacing: 0.025em;
}

.status-en-cours {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #3b82f6;
}

.status-succes {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #22c55e;
}

.status-erreur {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #ef4444;
}

.status-alerte {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #f59e0b;
}

/* Enhanced Shadows */
.shadow-sm {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* Loading Animation */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.card:hover .border-start {
  animation: shimmer 1.5s ease-in-out infinite;
}

/* Compact responsive adjustments */
@media (max-width: 992px) {
  .card {
    max-height: 260px;
  }
  
  .card-header {
    padding: 0.875rem 0.875rem 0.5rem;
  }
  
  .card-body {
    padding: 0.5rem 0.875rem;
  }
  
  .card-footer {
    padding: 0.625rem 0.875rem;
  }
}

@media (max-width: 576px) {
  .card {
    max-height: none;
  }
}

/* Timeline Modal Improvements */
.modal-title {
  color: #0f172a;
  font-weight: 600;
}

.timeline .event p {
  color: #374151;
  font-weight: 500;
}

/* Form Modal Improvements */
.form-label {
  color: #374151;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-label-cus {
  color: #374151;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-control,
.form-select {
  border: 1px solid #d1d5db;
  color: #374151;
}

.form-control:focus,
.form-select:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
  color: #111827;
}

.form-check-label {
  color: #374151;
  font-weight: 500;
}

/* Modal Footer Buttons */
.modal-footer .btn-secondary {
  background: #6b7280;
  color: white;
  border: none;
}

.modal-footer .btn-secondary:hover {
  background: #4b5563;
}

.modal-footer .btn-warning {
  background: #f59e0b;
  color: white;
  border: none;
  font-weight: 600;
}

.modal-footer .btn-warning:hover {
  background: #d97706;
}

.modal-footer .btn-warning:disabled {
  background: #d1d5db;
  color: #9ca3af;
}
