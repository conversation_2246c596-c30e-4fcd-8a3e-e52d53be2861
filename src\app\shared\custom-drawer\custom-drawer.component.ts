import { 
  Component, 
  EventEmitter, 
  Input, 
  Output, 
  ElementRef, 
  ViewChild, 
  HostListener, 
  ContentChild, 
  AfterContentInit, 
  OnInit, 
  OnDestroy,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  NgZone
} from '@angular/core';

@Component({
  selector: 'app-custom-drawer',
  templateUrl: './custom-drawer.component.html',
  styleUrls: ['./custom-drawer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CustomDrawerComponent implements AfterContentInit, OnInit, OnDestroy {  @Input() isOpen = false;
  @Input() width = '600px';
  @Input() title = '';
  @Input() closeOnBackdropClick = true;
  @Input() closeOnEscKey = true;
  @Input() position: 'left' | 'right' = 'right';
  @Input() zIndex = 1002;

  @Output() isOpenChange = new EventEmitter<boolean>();
  @Output() closed = new EventEmitter<void>();
  @ViewChild('drawerContent', { static: false }) drawerContent!: ElementRef<HTMLElement>;
  @ContentChild('[drawer-title]', { static: false }) customTitleContent: any;
  @ContentChild('[drawer-footer]', { static: false }) customFooterContent: any;
  
  hasCustomTitle = false;
  hasFooterContent = false;
  initialized = false;
  isShaking = false;
  
  private initTimeout?: number;
  private shakeTimeout?: number;
  private lastInteractionTime = 0;
  private readonly INTERACTION_DEBOUNCE = 100; // Debounce interactions
  
  constructor(
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone,
    private elementRef: ElementRef<HTMLElement>
  ) {}
  ngOnInit() {
    // Delay initialization to prevent flash on page load
    this.initTimeout = window.setTimeout(() => {
      this.initialized = true;
      this.cdr.markForCheck();
    }, 100);
    
    // Set initial accessibility attributes
    this.setAccessibilityAttributes();
  }

  ngOnDestroy() {
    if (this.initTimeout) {
      clearTimeout(this.initTimeout);
    }
    if (this.shakeTimeout) {
      clearTimeout(this.shakeTimeout);
    }
  }
  ngAfterContentInit() {
    // Check if custom title content exists
    this.hasCustomTitle = !!this.customTitleContent;
    this.hasFooterContent = !!this.customFooterContent;
    this.cdr.markForCheck();
  }

  private setAccessibilityAttributes(): void {
    const element = this.elementRef.nativeElement;
    element.setAttribute('role', 'dialog');
    element.setAttribute('aria-modal', 'true');
    if (this.title) {
      element.setAttribute('aria-label', this.title);
    }
  }
  @HostListener('click', ['$event'])
  onClick(event: MouseEvent): void {
    const now = Date.now();
    if (now - this.lastInteractionTime < this.INTERACTION_DEBOUNCE) {
      return;
    }
    this.lastInteractionTime = now;

    const target = event.target as HTMLElement;
    if (this.isOpen && this.drawerContent && !this.drawerContent.nativeElement.contains(target)) {
      if (this.closeOnBackdropClick) {
        this.close();
      } else {
        this.shakeDrawer();
      }
      event.stopPropagation();
    }
  }
  @HostListener('document:keydown.escape', ['$event'])
  onEscapePress(event: KeyboardEvent): void {
    if (!this.isOpen) return;
    
    const now = Date.now();
    if (now - this.lastInteractionTime < this.INTERACTION_DEBOUNCE) {
      return;
    }
    this.lastInteractionTime = now;

    if (this.closeOnEscKey) {
      this.close();
    } else {
      this.shakeDrawer();
    }
    event.preventDefault();
  }

  shakeDrawer(): void {
    if (this.isShaking) return;
    
    this.ngZone.runOutsideAngular(() => {
      this.isShaking = true;
      this.cdr.markForCheck();
      
      this.shakeTimeout = window.setTimeout(() => {
        this.ngZone.run(() => {
          this.isShaking = false;
          this.cdr.markForCheck();
        });
      }, 500);
    });
  }
  toggle(state?: boolean): void {
    const newState = state !== undefined ? state : !this.isOpen;
    if (this.isOpen === newState) return; // Prevent unnecessary updates
    
    this.isOpen = newState;
    this.isOpenChange.emit(this.isOpen);
    
    // Update accessibility attributes
    this.elementRef.nativeElement.setAttribute('aria-hidden', (!this.isOpen).toString());
    
    if (!this.isOpen) {
      this.closed.emit();
    }
    
    // Focus management for accessibility
    if (this.isOpen) {
      this.ngZone.runOutsideAngular(() => {
        setTimeout(() => {
          this.focusFirstElement();
        }, 300); // Wait for animation to complete
      });
    }
    
    this.cdr.markForCheck();
  }

  open(): void {
    this.toggle(true);
  }

  close(): void {
    this.toggle(false);
  }

  stopPropagation(event: Event): void {
    event.stopPropagation();
  }

  private focusFirstElement(): void {
    if (!this.drawerContent) return;
    
    const focusableElements = this.drawerContent.nativeElement.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    if (focusableElements.length > 0) {
      (focusableElements[0] as HTMLElement).focus();
    }
  }
}
