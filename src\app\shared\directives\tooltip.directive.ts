import {
  Directive,
  Input,
  ElementRef,
  ViewContainerRef,
  ComponentRef,
  OnDestroy,
  OnInit,
  TemplateRef,
  HostListener,
  Renderer2,
  NgZone,
  Injector,
  ComponentFactoryResolver,
  ApplicationRef,
  EmbeddedViewRef
} from '@angular/core';
import { computePosition, flip, shift, offset, arrow, autoUpdate, Placement, limitShift, size } from '@floating-ui/dom';
import { TooltipComponent } from '../tooltip/tooltip.component';

export interface TooltipConfig {
  content?: string;
  template?: TemplateRef<any>;
  placement?: Placement;
  delay?: number;
  hideDelay?: number;
  disabled?: boolean;
  showOnCreate?: boolean;
  interactive?: boolean;
  maxWidth?: string;
  theme?: 'dark' | 'light' | 'custom';
  animation?: 'fade' | 'scale' | 'slide' | 'none';
  trigger?: 'hover' | 'click' | 'focus' | 'manual';
  offset?: number;
  arrow?: boolean;
  className?: string;
}

@Directive({
  selector: '[appTooltip]',
  exportAs: 'tooltip',

})
export class TooltipDirective implements OnInit, OnDestroy {
  @Input('appTooltip') content: string = '';
  @Input() tooltipTemplate: TemplateRef<any> | null = null;
  @Input() tooltipPlacement: Placement = 'bottom-end';
  @Input() tooltipDelay: number = 300;
  @Input() tooltipHideDelay: number = 100;
  @Input() tooltipDisabled: boolean = false;
  @Input() tooltipInteractive: boolean = true;
  @Input() tooltipMaxWidth: string = '300px';
  @Input() tooltipTheme: 'dark' | 'light' | 'custom' = 'light';
  @Input() tooltipAnimation: 'fade' | 'scale' | 'slide' | 'none' = 'slide';
  @Input() tooltipTrigger: 'hover' | 'click' | 'focus' | 'manual' = 'hover';
  @Input() tooltipOffset: number = 8;
  @Input() tooltipArrow: boolean = true;
  @Input() tooltipClassName: string = '';
  @Input() tooltipContext: any = {};

  private componentRef: ComponentRef<TooltipComponent> | null = null;
  private tooltipElement: HTMLElement | null = null;
  private showTimeout: any;
  private hideTimeout: any;
  private cleanup: (() => void) | null = null;
  private isVisible = false;
  private mouseInTooltip = false;
  private mouseInTrigger = false;
  private documentClickListener: (() => void) | null = null;

  constructor(
    private elementRef: ElementRef,
    private viewContainerRef: ViewContainerRef,
    private renderer: Renderer2,
    private ngZone: NgZone,
    private injector: Injector,
    private componentFactoryResolver: ComponentFactoryResolver,
    private appRef: ApplicationRef
  ) {}

  ngOnInit() {
    this.setupAccessibility();
    this.setupTriggers();
  }

  ngOnDestroy() {
    this.hide();
    this.cleanup?.();
    this.clearTimeouts();
    this.removeDocumentClickListener();
  }

  private setupAccessibility() {
    const element = this.elementRef.nativeElement;
    
    // Add describedby for screen readers
    if (!element.getAttribute('aria-describedby')) {
      const id = `tooltip-${Math.random().toString(36).substr(2, 9)}`;
      this.renderer.setAttribute(element, 'aria-describedby', id);
    }

    // Make focusable if not already
    if (!element.hasAttribute('tabindex') && !this.isFocusable(element)) {
      this.renderer.setAttribute(element, 'tabindex', '0');
    }
  }

  private isFocusable(element: HTMLElement): boolean {
    const focusableElements = [
      'a[href]', 'button', 'input', 'textarea', 'select', 
      '[tabindex]:not([tabindex="-1"])', '[contenteditable]'
    ];
    return focusableElements.some(selector => element.matches(selector));
  }

  private setupTriggers() {
    const element = this.elementRef.nativeElement;

    switch (this.tooltipTrigger) {
      case 'hover':
        this.renderer.listen(element, 'mouseenter', () => this.onMouseEnter());
        this.renderer.listen(element, 'mouseleave', () => this.onMouseLeave());
        this.renderer.listen(element, 'focus', () => this.onFocus());
        this.renderer.listen(element, 'blur', () => this.onBlur());
        break;
      case 'click':
        this.renderer.listen(element, 'click', (e) => this.onClick(e));
        break;
      case 'focus':
        this.renderer.listen(element, 'focus', () => this.onFocus());
        this.renderer.listen(element, 'blur', () => this.onBlur());
        break;
    }

    // Always listen for escape key
    this.renderer.listen('document', 'keydown', (e) => this.onKeydown(e));
  }

  @HostListener('mouseenter')
  onMouseEnter() {
    if (this.tooltipTrigger === 'hover') {
      this.mouseInTrigger = true;
      this.scheduleShow();
    }
  }

  @HostListener('mouseleave')
  onMouseLeave() {
    if (this.tooltipTrigger === 'hover') {
      this.mouseInTrigger = false;
      this.scheduleHide();
    }
  }

  @HostListener('focus')
  onFocus() {
    if (this.tooltipTrigger === 'hover' || this.tooltipTrigger === 'focus') {
      this.scheduleShow();
    }
  }

  @HostListener('blur')
  onBlur() {
    if (this.tooltipTrigger === 'hover' || this.tooltipTrigger === 'focus') {
      this.scheduleHide();
    }
  }

  onClick(event: Event) {
    if (this.tooltipTrigger === 'click') {
      event.preventDefault();
      event.stopPropagation();
      this.toggle();
    }
  }

  onKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape' && this.isVisible) {
      this.hide();
      this.elementRef.nativeElement.focus();
    }
  }

  private scheduleShow() {
    if (this.tooltipDisabled || !this.hasContent()) return;

    this.clearTimeouts();
    this.showTimeout = setTimeout(() => {
      this.show();
    }, this.tooltipDelay);
  }

  private scheduleHide() {
    this.clearTimeouts();
    
    if (this.tooltipInteractive) {
      this.hideTimeout = setTimeout(() => {
        if (!this.mouseInTooltip && !this.mouseInTrigger) {
          this.hide();
        }
      }, this.tooltipHideDelay);
    } else {
      this.hideTimeout = setTimeout(() => {
        this.hide();
      }, this.tooltipHideDelay);
    }
  }

  private clearTimeouts() {
    if (this.showTimeout) {
      clearTimeout(this.showTimeout);
      this.showTimeout = null;
    }
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }
  }

  private hasContent(): boolean {
    return !!(this.content || this.tooltipTemplate);
  }

  show() {
    if (this.isVisible || this.tooltipDisabled || !this.hasContent()) return;

    this.createTooltip();
    this.isVisible = true;

    // Add document click listener for click trigger
    if (this.tooltipTrigger === 'click') {
      this.addDocumentClickListener();
    }
  }

  hide() {
    if (!this.isVisible) return;

    this.cleanup?.();
    this.cleanup = null;

    if (this.componentRef) {
      this.appRef.detachView(this.componentRef.hostView);
      this.componentRef.destroy();
      this.componentRef = null;
    }

    if (this.tooltipElement && this.tooltipElement.parentNode) {
      this.tooltipElement.parentNode.removeChild(this.tooltipElement);
      this.tooltipElement = null;
    }

    this.removeDocumentClickListener();
    this.isVisible = false;
  }

  toggle() {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  private addDocumentClickListener() {
    this.documentClickListener = this.renderer.listen('document', 'click', (event) => {
      if (!this.elementRef.nativeElement.contains(event.target) &&
          (!this.tooltipElement || !this.tooltipElement.contains(event.target))) {
        this.hide();
      }
    });
  }

  private removeDocumentClickListener() {
    if (this.documentClickListener) {
      this.documentClickListener();
      this.documentClickListener = null;
    }
  }

  private createTooltip() {
    // Create component factory
    const factory = this.componentFactoryResolver.resolveComponentFactory(TooltipComponent);
    
    // Create component
    this.componentRef = factory.create(this.injector);

    // Configure component
    const instance = this.componentRef.instance;
    instance.content = this.content;
    instance.template = this.tooltipTemplate;
    instance.context = this.tooltipContext;
    instance.maxWidth = this.tooltipMaxWidth;
    instance.theme = this.tooltipTheme;
    instance.animation = this.tooltipAnimation;
    instance.showArrow = this.tooltipArrow;
    instance.className = this.tooltipClassName;

    // Attach to application
    this.appRef.attachView(this.componentRef.hostView);

    // Get DOM element - need to find the actual tooltip div
    const hostElement = (this.componentRef.hostView as EmbeddedViewRef<any>).rootNodes[0] as HTMLElement;
    this.tooltipElement = hostElement.querySelector('.tooltip') as HTMLElement;
    
    // If tooltip element not found, use the host element directly
    if (!this.tooltipElement) {
      this.tooltipElement = hostElement;
    }

    // Add to DOM
    document.body.appendChild(hostElement);

    // Add visible class to show tooltip
    this.renderer.addClass(this.tooltipElement, 'tooltip--visible');

    // Setup interactive behavior
    if (this.tooltipInteractive) {
      this.setupInteractiveTooltip();
    }

    // Position tooltip
    this.positionTooltip();

    // Trigger change detection
    this.componentRef.changeDetectorRef.detectChanges();
  }

  private setupInteractiveTooltip() {
    if (!this.tooltipElement) return;

    this.renderer.listen(this.tooltipElement, 'mouseenter', () => {
      this.mouseInTooltip = true;
      this.clearTimeouts();
    });

    this.renderer.listen(this.tooltipElement, 'mouseleave', () => {
      this.mouseInTooltip = false;
      this.scheduleHide();
    });
  }

 private positionTooltip() {
  if (!this.tooltipElement) return;

  const referenceElement = this.elementRef.nativeElement;
  const floatingElement = this.tooltipElement;
  const arrowElement = floatingElement.querySelector('.tooltip-arrow') as HTMLElement;

  // Define all possible placements for better fallback
  const allPlacements: Placement[] = [
    'top', 'top-start', 'top-end',
    'bottom', 'bottom-start', 'bottom-end', 
    'left', 'left-start', 'left-end',
    'right', 'right-start', 'right-end'
  ];

  const middleware = [
    offset(this.tooltipOffset),
    flip({
      fallbackPlacements: allPlacements.filter(p => p !== this.tooltipPlacement),
      padding: 20
    }),
    shift({ 
      padding: 20,
      limiter: limitShift({
        offset: 10
      })
    }),
    size({
      apply:({ availableWidth, availableHeight, elements }) =>{
        const maxWidth = Math.min(availableWidth - 40, parseInt(this.tooltipMaxWidth) || 300);
        const maxHeight = Math.min(availableHeight - 40, 400);
        
        Object.assign(elements.floating.style, {
          maxWidth: `${maxWidth}px`,
          maxHeight: `${maxHeight}px`,
          overflow: 'auto',
          wordWrap: 'break-word',
          hyphens: 'auto'
        });
      },
      padding: 20
    })
  ];

  if (this.tooltipArrow && arrowElement) {
    middleware.push(arrow({ 
      element: arrowElement,
      padding: 12
    }));
  }

  this.ngZone.runOutsideAngular(() => {
    this.cleanup = autoUpdate(referenceElement, floatingElement, () => {
      computePosition(referenceElement, floatingElement, {
        placement: this.tooltipPlacement,
        middleware,
        strategy: 'fixed'
      }).then(({ x, y, placement, middlewareData }) => {
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const tooltipRect = floatingElement.getBoundingClientRect();
        
        const minDistance = 20;
        let finalX = Math.max(minDistance, Math.min(x, viewportWidth - tooltipRect.width - minDistance));
        let finalY = Math.max(minDistance, Math.min(y, viewportHeight - tooltipRect.height - minDistance));
        
        // Apply final position
        Object.assign(floatingElement.style, {
          left: `${finalX}px`,
          top: `${finalY}px`,
          zIndex: '9999',
          position: 'fixed'
        });

        // Make sure tooltip is visible
        this.renderer.addClass(floatingElement, 'tooltip--visible');

        // Update arrow position
        if (this.tooltipArrow && arrowElement && middlewareData.arrow) {
          const { x: arrowX, y: arrowY } = middlewareData.arrow;
          const staticSide = {
            top: 'bottom',
            right: 'left', 
            bottom: 'top',
            left: 'right',
          }[placement.split('-')[0]];

          Object.assign(arrowElement.style, {
            left: arrowX != null ? `${arrowX}px` : '',
            top: arrowY != null ? `${arrowY}px` : '',
            right: '',
            bottom: '',
            position: 'absolute'
          });
          
          if (staticSide) {
            arrowElement.style[staticSide] = '-4px';
          }
        }

        floatingElement.setAttribute('data-placement', placement);
      }).catch((error) => {
        console.warn('Tooltip positioning error:', error);
        Object.assign(floatingElement.style, {
          left: '50%',
          top: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: '9999',
          position: 'fixed'
        });
        this.renderer.addClass(floatingElement, 'tooltip--visible');
      });
    });
  });
}
}