interface ICriteriaAlertProduit {
 codeProduit: string;
 ouvertFermer: boolean;
}

interface IAlertProduit {
  ancienneValeur: string;
  champModifie: string;
  cloturePar: number;
  codeSite: number;
  dateAlerte: string;
  dateClotureAlerte: string;
  id: number;
  nouvelleValeur: string;
  proCode: string;
}


export class CriteriaAlertProduit implements ICriteriaAlertProduit {
    codeProduit: string;
    ouvertFermer: boolean;
    constructor(criteria: Partial<ICriteriaAlertProduit>) {
        Object.assign(this, criteria);
    }
}

export class AlertProduit implements IAlertProduit {
  ancienneValeur: string;
  champModifie: string;
  cloturePar: number;
  codeSite: number;
  dateAlerte: string;
  dateClotureAlerte: string;
  id: number;
  nouvelleValeur: string;
  proCode: string;

  constructor(alertProduit: Partial<IAlertProduit>) {
    Object.assign(this, alertProduit);
  }
}

