import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment as env } from "src/environments/environment";
import { PlateformUsageCriteria, Platform,PlatformUsage } from "../models/platform.model";

@Injectable({
  providedIn: 'root'
})
export class WinClientPlatformService {
 
    constructor(
        private http: HttpClient
    ) { }
    
    getPlatformUsage(codeGroupe: string): Observable<PlatformUsage> {
        return this.http.get<PlatformUsage>(`${env.winclient_base_url}/api/winclient/plateformes/usage/${codeGroupe}`);
    }

    runSync(criteria: PlateformUsageCriteria): Observable<any> {
        return this.http.post<any>(`${env.winclient_base_url}/api/winclient/plateformes/sync`, criteria);
    }

    getAllPlatforms(): Observable<Platform[]> {
        return this.http.get<Platform[]>(`${env.winclient_base_url}/api/winclient/plateformes`);
    }
}