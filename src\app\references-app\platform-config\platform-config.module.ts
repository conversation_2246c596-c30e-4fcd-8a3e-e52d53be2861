import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PlatformConfigRoutingModule } from './platform-config-routing.module';
import { ExecutionBatchComponent } from './pages/execution-batch/execution-batch.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/shared/shared.module';
import { ReferentialModule } from "../referential/referential.module";
import { GestionUtilisateurComponent } from './pages/gestion-utilisateur/gestion-utilisateur.component';
import { GridModule } from '@progress/kendo-angular-grid';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    GridModule,
    ReactiveFormsModule,
    PlatformConfigRoutingModule,
    SharedModule,
    ReferentialModule
],
  declarations: [ExecutionBatchComponent,GestionUtilisateurComponent],
})
export class PlatformConfigModule { }
