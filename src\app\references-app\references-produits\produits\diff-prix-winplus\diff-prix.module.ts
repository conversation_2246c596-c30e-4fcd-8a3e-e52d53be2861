import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProduitWinplusRoutingModule } from './diff-prix-routing.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GridModule } from '@progress/kendo-angular-grid';
import { SharedModule } from "../../../../shared/shared.module";
import { NgbTypeaheadModule } from '@ng-bootstrap/ng-bootstrap';
import { DiffPrixWinplusComponent } from './list-diff-winplus/diff-prix-winplus.component';

@NgModule({
  imports: [
    CommonModule,
    ProduitWinplusRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    GridModule,
    SharedModule,
    NgbTypeaheadModule
],
  declarations: [DiffPrixWinplusComponent]
})
export class ProduitFournisseurModule { }
