
<app-custom-drawer 
[isOpen]="showFilter"
[title]="'Filtrer Produit ' + (isGroupePage ? 'du Groupe' : 'Des Sites')"
(isOpenChange)="showFilter = $event"
>
    <form class="p-2" [formGroup]="filterForm" (ngSubmit)="filterSubmit()" id="filterForm"   appFocusTrap>
      <div class="flex-grow-1">
        <div class="row" *ngIf="!isGroupePage">
          <div class="col-md-12">
            <div class="mb-2">
              <label class="form-label">Site</label>
              <app-custom-select
                [options]="selectSites"
                [multiSelect]="true"
                [showClearButton]="true"
                [placeholder]="'Sélectionner un ou plusieurs sites'"
                (valueChange)="onSiteChange($event)">
              </app-custom-select>
              <!-- <select name="" id="" class="form-select" formControlName="codeSite" [compareWith]="compareFn">
                <option *ngFor="let site of sites" [value]="site.code_site" [ngValue]="site.code_site">{{site.nom}} ({{site.code_site}})</option>
              </select> -->

         
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="mb-2">
              <label class="form-label">Designation</label>
              <input type="search" class="form-control" formControlName="designation" placeholder="tapez désignation">
            </div>
          </div>
          <div class="col-md-6" *ngIf="!isGroupePage">
                <div class="mb-2">
                  <label class="form-label">Code produit site</label>
                  <input type="search" class="form-control" formControlName="codeProSite" placeholder="tapez code produit site">
                </div>
                
          </div>
         
          <div class="col-md-6" *ngIf="isGroupePage">
                <div class="mb-2">
                  <label class="form-label">Code Sophatel</label>
                  <input type="search" class="form-control" formControlName="codeSophatel" placeholder="tapez code code Sophatel">
                </div>
                
          </div>
         
        </div>

        <div class="row">
  
          <div class="col-md-6">
            <div class="mb-2">
              <label class="form-label">Code Groupe</label>
              <input type="search" class="form-control" formControlName="codeGroupe" placeholder="tapez code groupe">
            </div>
          </div>
           <div class="col-md-6">
            <div class="mb-2">
              <label class="form-label">Code winplus</label>
              <input type="search" class="form-control" formControlName="codeWinplus" placeholder="tapez code winplus">
            </div>
          </div>
              <!--  -->
        </div>
        <div class="row">
  
          <div class="col-md-6">
            <div class="mb-2">
              <label class="form-label">Laboratoire</label>
              <input type="search" class="form-control" formControlName="labelleLabo" placeholder="tapez laboratoire">
            </div>
          </div>
           <div class="col-md-6">
            <div class="mb-2">
              <label class="form-label">Code Barre</label>
              <input type="search" class="form-control" formControlName="codeBarre" placeholder="tapez code barre">
            </div>
          </div>
              <!--  -->
        </div>

        
        <div class="row">
  
          <div class="col-md-6">
            <div class="mb-2">
              <label class="form-label">Date création Du</label>
              <app-date-picker formControlName="dateCreateDu"></app-date-picker>
            </div>
          </div>
           <div class="col-md-6">
            <div class="mb-2">
              <label class="form-label">Date création Au</label>
          <app-date-picker formControlName="dateCreateAu"></app-date-picker>
            </div>
          </div>
              <!--  -->
        </div>
        <div class="row flex-column  gap-2">
          <div *ngIf="!isGroupePage" class="col-12 d-flex flex-column flex-sm-row align-items-start align-items-sm-center mb-2 mb-sm-0">
                <label for="groupement" class="form-label me-2 mb-1 mb-sm-0 flex-shrink-0">Transco Groupe</label>
                <div class="w-100">
                        <app-switch
                [elements]='[{ label: "Tous", value:null},{ label: "Groupé", value:true},{ label: "Non Groupé", value:false}]'
                formControlName="transcoCodeGroupe" name="groupement" [disabled]="false"
                [switchClass]="'info'"></app-switch>
                </div>
          
          </div>
          <div class="col-12 d-flex flex-column flex-sm-row align-items-start align-items-sm-center mb-2 mb-sm-0">
                <label for="groupementWinpluus" class="form-label me-2 mb-1 mb-sm-0 flex-shrink-0">Transco Winplus</label>
                <div class="w-100">
                        <app-switch
                [elements]='[{ label: "Tous", value:null},{ label: "Transcodé", value:true},{ label: "Non Transcodé", value:false}]'
                formControlName="transcoWinplus" name="groupementWinpluus" [disabled]="false"
                [switchClass]="'info'"></app-switch>
                </div>
          
          </div>
          <div class="col-12 d-flex flex-column flex-sm-row align-items-start align-items-sm-center mb-2 mb-sm-0">
                <label for="produitSupp" class="form-label me-2 mb-1 mb-sm-0 flex-shrink-0">Supprimé</label>
                <div class="w-100">
                        <app-switch
                [elements]='[{ label: "Tous", value:null},{ label: "Supprimé", value:true},{ label: "Non Supprimé", value:false}]'
                formControlName="produitSupp" name="produitSupp" [disabled]="false"
                [switchClass]="'info'"></app-switch>
                </div>
          
          </div>
          <div *ngIf="isGroupePage" class="col-12 d-flex flex-column flex-sm-row align-items-start align-items-sm-center mb-2 mb-sm-0">
                <label for="envoyer" class="form-label me-2 mb-1 mb-sm-0 flex-shrink-0">Envoyé</label>
                <div class="w-100">
                        <app-switch
                [elements]='[{ label: "Tous", value:null},{ label: "Envoyé", value:true},{ label: "Non Envoyé", value:false}]'
                formControlName="envoyer" name="envoyer" [disabled]="false"
                [switchClass]="'info'"></app-switch>
                </div>
          
          </div>
          <div *ngIf="isGroupePage" class="col-12 d-flex flex-column flex-sm-row align-items-start align-items-sm-center mb-2 mb-sm-0">
                <label for="envoyer" class="form-label me-2 mb-1 mb-sm-0 flex-shrink-0">Active</label>
                <div class="w-100">
                        <app-switch
                [elements]='[{ label: "Tous", value:null},{ label: "Actif", value:true},{ label: "Non Actif", value:false}]'
                formControlName="estActif" name="estActif" [disabled]="false"
                [switchClass]="'info'"></app-switch>
                </div>
          
          </div>
        </div>
      </div>
   
    </form>
       <div class="modal-footer bg-white justify-content-start" drawer-footer>
        <div class="col-12 d-flex flex-wrap gap-2 justify-content-start">
          <button class="btn btn-primary" tabindex="-1" type="submit" form="filterForm">Recherche</button>
          <button class="btn btn-dark" tabindex="-1" type="button" (click)="clearFilter()"  >Vider</button>
        </div>
      </div>
</app-custom-drawer>

<app-custom-drawer
[isOpen]="showTranscoDrawer"
[width]="'70%'"
[title]="'Produit'  + (isGroupePage ? 'Groupe' : 'Site') + ' Transcodage'"
(isOpenChange)="closeTranscoDrawerAndReset()"
>
   <div class="modal-body pb-0 d-flex flex-column" style="height: calc(100% - 46px); overflow: auto;">
    <kendo-grid   [kendoGridBinding]="[clickedItem]"
    [ngClass]="{'client-have-association-grid': true}"
    *ngIf="clickedItem && clickedItem?.codeSite != 0"
    class=" content-wrap flex-shrink-0" style="max-height: 200px;height: auto !important;" >
      <ng-template kendoGridToolbarTemplate>
        <div [ngClass]="{'client-have-association-bg':true}" style="height: 44px;" class="d-flex justify-content-between align-items-center px-2">
          <span class="text-white fs-4 k-font-weight-bold d-flex align-items-center k-gap-1">
            <i class="mdi" style="font-size: 23px;line-height: 0;" [ngClass]="{'mdi-checkbox-marked-outline': clickedItem?.codeGroupe, 'mdi-close-circle-outline': !clickedItem?.codeGroupe}"></i>
            Produit Site ({{clickedItem?.codeGroupe ? "Produit déjà Transcodé" :'Produit non Transcodé'}}) </span>

        <button class="btn btn-danger btn-sm rounded-pill m-1"   style="padding: 2px 6px;" *ngIf="clickedItem?.codeGroupe"
        (click)="detachProductGroupeTranscoded()"
        >
          <i class="mdi mdi-link-variant-off"></i>
          Détacher</button>
        </div>
      </ng-template>
      <kendo-grid-column field="codeSite" title="Code Site" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
     <span [ngClass]="{'text-success font-weight-bold': dataItem?.actifVente}">
        {{dataItem.codeSite | sitePipe}}
      </span>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="idSource" title="Code Local" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          <app-copy-cell [value]="dataItem.idSource"></app-copy-cell>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="codeWinplus" title="Code Winplus" [width]="70"></kendo-grid-column>
      <kendo-grid-column field="codeGroupe" title="Code Groupe" [width]="70"></kendo-grid-column>
      <kendo-grid-column field="designation" title="Designation" [width]="200">
        <ng-template kendoGridCellTemplate let-dataItem>
          <app-copy-cell [value]="dataItem.designation"></app-copy-cell>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="prixAchatStd" title="PPH" [width]="70"></kendo-grid-column>
      <kendo-grid-column field="prixVenteStd" title="PPV" [width]="70"></kendo-grid-column>
      <kendo-grid-column field="codeBarre" title="Code Barre"  class="text-end"  [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          <app-copy-cell [value]="dataItem.codeBarre"></app-copy-cell>
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>

    <div class="mt-2">
      <kendo-grid class="content-wrap flex-shrink-0 ref-grid" style="max-height: 200px;height:auto !important;" [kendoGridBinding]="ProductGroupeTranscoded" *ngIf="clickedItem?.codeGroupe && !isTranscoGroupeLoading && ProductGroupeTranscoded?.length >0">
        <ng-template kendoGridToolbarTemplate>
          <div [ngClass]="{'client-have-association-bg':false,'client-have-no-association-bg':true}" style="height: 44px;" class="d-flex justify-content-between align-items-center px-2">
            <span class="text-white fs-4 k-font-weight-bold">Produit Groupe</span>
          <button class="btn btn-warning btn-sm rounded-pill m-1" (click)="detacheWinplusProduit()" style="padding: 2px 6px;" *ngIf="clickedItem?.codeWinplus && clickedItem?.codeSite == 0">
              <i class="mdi mdi-link-variant-off"></i>
              Détacher</button>
          </div>
        </ng-template>
        <kendo-grid-column field="codeSite" title="Code Site" [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
        <span [ngClass]="{'text-success font-weight-bold': dataItem?.actifVente}">
        {{dataItem.codeSite | sitePipe}}
      </span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="idSource" title="Code Local" [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.idSource"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="codeWinplus" title="Code Winplus" [width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeWinplus"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="codeGroupe" title="Code Groupe" [width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeGroupe"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="designation" title="Designation" [width]="200">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.designation"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixAchatStd" title="PPH" [width]="70" class="text-end">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{dataItem.prixAchatStd | number: "1.2-2": "Fr-fr"}}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixVenteStd" title="PPV" [width]="70" class="text-end">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{dataItem.prixVenteStd | number: "1.2-2": "Fr-fr"}}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="codeBarre" title="Code Barre"  class="text-end"  [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeBarre"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
      <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="isTranscoGroupeLoading">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-loading mdi-spin text-primary fs-1"></i>
        </div>
        <p class="font-weight-600 m-0">Chargement de produit groupe...</p>
       </div>
       <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="!isTranscoGroupeLoading && clickedItem?.codeGroupe && !ProductGroupeTranscoded?.length">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-alert-circle-outline text-warning fs-1"></i>
        </div>
        <p class="font-weight-600 m-0">Ce produit a déjà un code groupe, mais ce produit n’existe pas dans la base groupe.</p>
        <!-- button to create new product -->
        <button class="btn btn-dark mt-2 rounded-pill" (click)="getProductGroupeTranscoded()">rafraîchir</button>
       </div>
      <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="!clickedItem?.codeGroupe && !isTranscoGroupeLoading && !showSearchProduitGroupe ">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-alert-circle-outline text-warning fs-1"></i>
        </div>
        <p class="font-weight-600 m-0">Ce Produit n'a aucun Produit Groupe Associé</p>
        <!-- refresh button -->
        <button class="btn btn-dark mt-2 rounded-pill" (click)="showSearchProductGroupe()">Transcode le Produit</button>
       </div>
       <ng-container *ngIf="showSearchProduitGroupe" [ngTemplateOutlet]="searchProductGroupeTemplate"></ng-container>
       <ng-container *ngIf="showSearchProduitWinplus && isGroupePage" [ngTemplateOutlet]="searchProductWinplusTemplate"></ng-container>

    </div>

 <div class="mt-2">
      <kendo-grid class="content-wrap flex-shrink-0 ref-grid have-winplus-association-grid" style="max-height: 200px;height:auto !important;" [kendoGridBinding]="ProductWinplusTranscoded" *ngIf="clickedItem?.codeWinplus && clickedItem?.codeGroupe && !isTranscoWinplusLoading && ProductGroupeTranscoded?.length > 0">
        <ng-template kendoGridToolbarTemplate>
          <div [ngClass]="{'client-have-association-bg':false,'winplus-have-association-bg':true}" style="height: 44px;" class="d-flex justify-content-between align-items-center px-2">
            <span class="text-white fs-4 k-font-weight-bold">Produit Winplus</span>
          </div>
        </ng-template>
        <kendo-grid-column field="codeSite" title="" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="idSource" title="" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="codeWinplus" title="Code Winplus" [width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeWinplus"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="" title="" [width]="70">
          <!-- <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeGroupe"></app-copy-cell>
          </ng-template> -->
        </kendo-grid-column>
        <kendo-grid-column field="designation" title="Designation" [width]="200">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.designation"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixAchatStd" title="PPH" class="text-end" [width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{dataItem.prixAchatStd | number: "1.2-2": "Fr-fr"}}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixVenteStd" title="PPV" class="text-end" [width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{dataItem.prixVenteStd | number: "1.2-2": "Fr-fr"}}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="codeBarre" title="Code Barre"  class="text-end"  [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeBarre"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
      <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="isTranscoWinplusLoading">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-loading mdi-spin text-primary fs-1"></i>
        </div>
        <p class="font-weight-600 m-0">Chargement de produit Winplus...</p>
       </div>
      <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="!clickedItem?.codeWinplus && clickedItem?.codeGroupe && !isTranscoWinplusLoading && ProductGroupeTranscoded?.length > 0 && !showSearchProduitWinplus">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-alert-circle-outline text-warning fs-1"></i>
        </div>
        <p class="font-weight-600 m-0">Ce Produit n'a aucun Produit Winplus Associé</p>
         <button class="btn btn-dark mt-2 rounded-pill" *ngIf="clickedItem?.codeWinplus" (click)="getProductWinplusTranscoded()">Rafraîchir</button>
        <button class="btn btn-dark mt-2 rounded-pill" *ngIf="!clickedItem?.codeWinplus" (click)="navigateToTranscodeWinplus()">Transcoder Avec Winplus </button>
       </div>
        <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="isLoadingsearchLinkedProductSite && isGroupePage">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-loading mdi-spin text-primary fs-1"></i>
        </div>
        <p class="font-weight-600 m-0">Chargement des produit site...</p>
       </div>
      <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="isGroupePage && productSiteLinkedToGroupe?.length === 0 && !isTranscoWinplusLoading && !isTranscoGroupeLoading && !isLoadingsearchLinkedProductSite && !isFetchedLinkedProductSite">
        <button class="btn btn-dark mt-2 rounded-pill" (click)="getAllProductSiteLinkedToGroupe()">
          <i class="mdi mdi-link"></i>
          Afficher tout les produits site liés</button>
      </div>
        <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center"  *ngIf="isFetchedLinkedProductSite && !isLoadingsearchLinkedProductSite">
          <div class="d-flex justify-content-center align-items-center">
            <i class="mdi mdi-alert-circle-outline text-warning fs-1"></i>
          </div>
          <p class="font-weight-600 m-0">Ce Produit Groupe n'a aucun Produits site Lié</p>
          <button class="btn btn-dark btn-sm mt-2 rounded-pill" (click)="getAllProductSiteLinkedToGroupe()">
            <i class="mdi mdi-refresh"></i>
            Rafraîchir</button>
       </div>
        <kendo-grid class="content-wrap flex-shrink-0 client-have-association-grid ref-grid mt-2 custom-sort-grid" 
        style="max-height: 500px;height:auto !important;"
         [kendoGridBinding]="productSiteLinkedToGroupe"  [sortable]="{ mode: 'single' }" 
         [sort]="productSiteLinkedToGroupeSort" 
          (sortChange)="onSortChangeProductSiteLinkedToGroupe($event)"

         *ngIf="!isLoadingsearchLinkedProductSite && isGroupePage && productSiteLinkedToGroupe?.length > 0">
        <ng-template kendoGridToolbarTemplate>
          <div [ngClass]="{'client-have-association-bg':true}" style="height: 44px;" class="d-flex justify-content-between align-items-center px-2">
            <span class="text-white fs-4 k-font-weight-bold">Produits site Liés</span>
            <!-- refresh button -->
            <button class="btn btn-dark btn-sm rounded-pill m-1" (click)="getAllProductSiteLinkedToGroupe()" style="padding: 2px 6px;">
              <i class="mdi mdi-refresh"></i>
              Rafraîchir</button>
          </div>
        </ng-template>
        <kendo-grid-column field="codeSite" title="Code Site" [width]="100">
          <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
            <app-grid-sort-header [title]="column.title"  [type]="'numeric'"
            [active]="linkedProductSiteNavigation.sortField === column.field"
            [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
          <span [ngClass]="{'text-success font-weight-bold': dataItem?.actifVente}">
            {{dataItem.codeSite | sitePipe}}
          </span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="idSource" title="Code Local" [width]="100">
          <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
            <app-grid-sort-header [title]="column.title"  [type]="'numeric'"
            [active]="linkedProductSiteNavigation.sortField === column.field"
            [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
             <app-copy-cell [value]="dataItem.idSource"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="codeWinplus" title="Code Winplus" [width]="70">
          <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"   [type]="'numeric'"
              [active]="linkedProductSiteNavigation.sortField === column.field"
              [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeWinplus"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="codeGroupe" title="Code Groupe" [width]="70">
           <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="linkedProductSiteNavigation.sortField === column.field"
              [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeGroupe"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="designation" title="Designation" [width]="200">
           <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="linkedProductSiteNavigation.sortField === column.field"
              [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.designation"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixAchatStd" class="text-end"  title="PPH" [width]="70">
           <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"   [type]="'numeric'"
              [active]="linkedProductSiteNavigation.sortField === column.field"
              [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
            {{dataItem.prixAchatStd | number: "1.2-2": "Fr-fr"}}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixVenteStd" class="text-end"  title="PPV" [width]="70">
           <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"   [type]="'numeric'"
              [active]="linkedProductSiteNavigation.sortField === column.field"
              [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
          {{dataItem.prixVenteStd | number: "1.2-2": "Fr-fr"}}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="codeBarre" title="Code Barre"  class="text-end"  [width]="100">
           <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="linkedProductSiteNavigation.sortField === column.field"
              [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeBarre"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
    </div>
  </div>
</app-custom-drawer>



<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">Produits {{isGroupePage ? 'du Groupe' : 'Des Sites'}}</h4>
      <div class="d-flex align-content-center align-items-center gap-1">
      <button
        type="button"
        class="btn btn-dark me-2 py-1 d-flex justify-content-center align-items-center"
        (click)="toggleFilter(true)"
        [attr.aria-expanded]="showFilter ? 'true' : 'false'"
        [attr.aria-controls]="showFilter ? 'filter' : ''"
      >
        <i class="mdi mdi-filter pe-1"></i>
        <span>Filtrer</span>
      </button>
      </div>

    </div>
  </div>
</div>


<div class="pe-2">
  <kendo-grid class="border-grey mt-2   header-wrap content-wrap fournisseur-product-grid custom-sort-grid"  
  [ngClass]="{'ref-grid': isGroupePage,'client-have-association-grid': !isGroupePage}"
    [data]="siteProducts" style="height: calc(100vh - 130px);border-radius: 10px;" 
    [pageable]="true"
    [pageSize]="navigation.pageSize"
    [skip]="navigation.skip"
    [rowClass]="rowClass"
    [sortable]="{mode: 'single'}"
    (sortChange)="onSortChange($event)"
    [sort]="siteProductSort"
    >
   <kendo-grid-column field="codeSite" title="Site" [width]="80" [sortable]="!isGroupePage">
    <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
      <div class="text-wrap text-start" *ngIf="isGroupePage">Site</div>
      <app-grid-sort-header [title]="column.title"    [type]="'numeric'"  *ngIf="!isGroupePage"
      [active]="navigation.sortField === column.field"
      [direction]="navigation.sortMethod"></app-grid-sort-header>
  </ng-template>
    <ng-template kendoGridCellTemplate let-dataItem>
      <span [ngClass]="{'text-success font-weight-bold': dataItem?.actifVente}">
        {{dataItem.codeSite | sitePipe}}
      </span>
      </ng-template>
   </kendo-grid-column>
    <kendo-grid-column [headerClass]="'text-start'" field="idSource" title="Code produit site" [width]="100" [hidden]="isGroupePage">
      <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"   [type]="'numeric'"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
   </ng-template>
    </kendo-grid-column>
    <kendo-grid-column [headerClass]="'text-start'"   field="produitExtensions.codeSpecialEchange" title="Code sophatel" [width]="100" [hidden]="!isGroupePage">
      <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"   [type]="'numeric'"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
   </ng-template>
    </kendo-grid-column>
    <kendo-grid-column [headerClass]="'text-start'" field="codeGroupe" title="Code Groupe" [width]="100">
        <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
          <app-grid-sort-header [title]="column.title"   [type]="'numeric'"  
          [active]="navigation.sortField === column.field"
          [direction]="navigation.sortMethod"></app-grid-sort-header>
     </ng-template>
    </kendo-grid-column>
    <kendo-grid-column [headerClass]="'text-start'" field="codeWinplus" title="Code winplus" [width]="100">
        <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
          <app-grid-sort-header [title]="column.title"    [type]="'numeric'" 
          [active]="navigation.sortField === column.field"
          [direction]="navigation.sortMethod"></app-grid-sort-header>
     </ng-template>
    </kendo-grid-column>
    <kendo-grid-column [headerClass]="'text-start'" field="designation" title="Designation"  [width]="400">
        <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
          <app-grid-sort-header [title]="column.title"  
          [active]="navigation.sortField === column.field"
          [direction]="navigation.sortMethod"></app-grid-sort-header>
     </ng-template>
    </kendo-grid-column>
    <kendo-grid-column [headerClass]="'text-start'" class="text-end" field="prixAchatStd" title="PPH" [width]="70">
        <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
          <app-grid-sort-header [title]="column.title"  
          [active]="navigation.sortField === column.field"  [type]="'numeric'" 
          [direction]="navigation.sortMethod"></app-grid-sort-header>
     </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
          {{dataItem.prixAchatStd  | number: "1.2-2": "Fr-fr"}}
        </ng-template>

    </kendo-grid-column>
    <kendo-grid-column field="prixVenteStd" title="PPV" class="text-end" [width]="70" [headerClass]="'text-start'">
        <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
          <app-grid-sort-header [title]="column.title"    [type]="'numeric'" 
          [active]="navigation.sortField === column.field"
          [direction]="navigation.sortMethod"></app-grid-sort-header>
     </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
          <!-- fr -->
          {{dataItem.prixVenteStd  | number: "1.2-2": "Fr-fr"}}
        </ng-template>
    </kendo-grid-column>
    <kendo-grid-column  field="dateCreation" title="Date création" [width]="100">
      <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"   [type]="'numeric'" 
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
     </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
          {{dataItem.dateCreation | date: 'dd/MM/yyyy'}}
        </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="produitExtensions.dateEnvoi" title="Date Envoie"  [width]="100" [hidden]="!isGroupePage">
      <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"   [type]="'numeric'" 
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
     </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
          {{dataItem.produitExtensions?.dateEnvoi | date: 'dd/MM/yyyy'}}
        </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="codeBarre" title="Code Barre" class="text-end" [width]="120" [headerClass]="'text-start'">
        <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
          <app-grid-sort-header [title]="column.title"  [type]="'numeric'" 
          [active]="navigation.sortField === column.field"
          [direction]="navigation.sortMethod"></app-grid-sort-header>
     </ng-template>
    </kendo-grid-column>
    <kendo-grid-column   title="Action" [width]="80" [headerClass]="'text-start'">
      <ng-template kendoGridCellTemplate let-dataItem>
         <app-action-icon [icon]="'magnify'" [extendClass]="'circle-lg'" (click)="openTranscoDrawer(dataItem)"></app-action-icon>
          <app-action-icon [backgroundColor]="dataItem?.dateDeclarationForProcess ? 'danger' : 'success'" [icon]="dataItem?.dateDeclarationForProcess ? 'calendar-remove' : 'calendar-check'" [extendClass]="'circle-lg'" (click)="handleMarkForProcess(dataItem)"></app-action-icon>
          <app-action-icon *ngIf="isGroupePage" [icon]="'pencil'"  [extendClass]="'circle-lg'" (click)="HandleEditProduitGroupeDesignation(dataItem)"></app-action-icon>
      </ng-template>
    </kendo-grid-column>


    <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
    let-total="total">
    <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"  [allowPageSizes]="true"
      [navigation]="navigation" style="width: 100%;"
      (pageChange)="pageChange($event)"></wph-grid-custom-pager>
  </ng-template>
  </kendo-grid>
</div>




<ng-template #searchProductGroupeTemplate>
  <div class="card" style="
    border: 1px solid #ccc !important;
    border-radius: 12px;
    overflow: hidden;
">


  <div class="card-header p-1 d-flex justify-content-between align-items-center">
    <h4 class="card-title m-0">Recherche Produit Groupe</h4>
     <i class="mdi mdi-18px mdi-close px-1 k-cursor-pointer" (click)="showSearchProduitGroupe = false"></i>
  </div>
  <div class="card-body p-0">
       <kendo-grid class="content-wrap flex-shrink-0 ref-grid" [data]="groupeProducts"
     [pageable]="true"
     [pageSize]="groupeProductNavigation.pageSize"
      [skip]="groupeProductNavigation.skip"
      [height]="500"
      [loading]="isSearchGroupeLoading"
     >
        <ng-template kendoGridToolbarTemplate>
          <div class="row p-1" appFocusTrap (keydown.enter)="searchProductGroupe()">
            <div class="col-md-3">
              <div>
              <label class="form-label fs-4 mb-0">Désignation</label>
              <input type="search" appAutoFocus class="form-control form-control-sm" [(ngModel)]="searchProduitGroupeModel.designation" placeholder="Recherche par désignation">
            </div>
            </div>
             <div class="col-md-3">
              <div>
              <label class="form-label fs-4 mb-0">Code Barre</label>
              <input type="search" class="form-control form-control-sm" [(ngModel)]="searchProduitGroupeModel.codeBarre" placeholder="Recherche par code barre">
            </div>
            </div>
             <div class="col-md-3">
              <div>
              <label class="form-label fs-4 mb-0">Laboratoire</label>
              <input type="search" class="form-control form-control-sm" [(ngModel)]="searchProduitGroupeModel.laboratoire" placeholder="Recherche par laboratoire">
            </div>
            </div>
             <div class="col-md-3">
              <div class="d-flex justify-content-end align-items-start flex-column h-100">
                <label class="form-label fs-4 mb-0"></label>
              <!-- <label class="form-label fs-4 mb-0">Code Groupe</label>
              <input type="text" class="form-control form-control-sm" placeholder="Recherche par code groupe"> -->
              <button class="btn btn-primary btn-sm" tabindex="-1" (click)="searchProductGroupe()">
              <i class="mdi mdi-magnify"></i> Rechercher
            </button>
            </div>
            </div>
          </div>
        
        </ng-template>
        <kendo-grid-column field="codeGroupe" title="Code Groupe" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="codeWinplus" title="Code Winplus" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="designation" title="Designation" [width]="200"></kendo-grid-column>
        <kendo-grid-column field="prixAchatStd" title="PPH" class="text-end" [width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{dataItem.prixAchatStd | number: "1.2-2": "Fr-fr"}}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixVenteStd" title="PPV" class="text-end" [width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{dataItem.prixVenteStd | number: "1.2-2": "Fr-fr"}}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="codeBarre" title="Code Barre" [width]="100"></kendo-grid-column>
        <kendo-grid-column [width]="100" >
          <ng-template kendoGridCellTemplate let-dataItem>
              <button class="btn btn-dark btn-sm rounded-pill" (click)="processTranscode(dataItem)">
                <i class="mdi mdi-link"></i>
              Transcoder
              </button>
          </ng-template>
        </kendo-grid-column>
    <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
        let-total="total">
        <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"  [allowPageSizes]="false"
          [navigation]="groupeProductNavigation" style="width: 100%;"
          (pageChange)="SearchProductGroupePageChange($event)"></wph-grid-custom-pager>
      </ng-template>
        </kendo-grid>
  </div> 
</div>
 </ng-template>





 <ng-template #searchProductWinplusTemplate>
  <div class="card mt-2" style="   
    border: 1px solid #ccc !important;
    border-radius: 12px;
    overflow: hidden;
">


  <div class="card-header p-1 d-flex justify-content-between align-items-center">
    <h4 class="card-title m-0">Recherche Produit Winplus</h4>
    <i class="mdi mdi-18px mdi-close px-1 k-cursor-pointer" (click)="showSearchProduitWinplus = false"></i>
  </div>
  <div class="card-body p-0">
       <kendo-grid class="content-wrap flex-shrink-0 ref-grid" [data]="winplusProducts"
     [pageable]="true"
     [pageSize]="winplusProductNavigation.pageSize"
      [skip]="winplusProductNavigation.skip"
      [height]="500"
      [loading]="isWinplusSearchLoading"
     >
        <ng-template kendoGridToolbarTemplate>
          <div class="row p-1" appFocusTrap (keydown.enter)="searchProductWinplus()">
            <div class="col-md-3">
              <div>
              <label class="form-label fs-4 mb-0">Désignation</label>
              <input type="search" appAutoFocus class="form-control form-control-sm" [(ngModel)]="searchProduitWinplusModel.designation" placeholder="Recherche par désignation">
            </div>
            </div>
             <div class="col-md-3">
              <div>
              <label class="form-label fs-4 mb-0">Code Barre</label>
              <input type="search" class="form-control form-control-sm" [(ngModel)]="searchProduitWinplusModel.codeBarre" placeholder="Recherche par code barre">
            </div>
            </div>
             <!-- <div class="col-md-3">
              <div>
              <label class="form-label fs-4 mb-0">Laboratoire</label>
              <input type="search" class="form-control form-control-sm" [(ngModel)]="searchProduitWinplusModel.laboratoire" placeholder="Recherche par laboratoire">
            </div>
            </div> -->
             <div class="col-md">
              <div class="d-flex justify-content-start align-items-end flex-row h-100 k-gap-1">
              <button class="btn btn-primary btn-sm" tabindex="-1" (click)="searchProductWinplus()">
              <i class="mdi mdi-magnify"></i> Rechercher
            </button>
              <button class="btn btn-warning btn-sm" type="button" tabindex="-1" (click)="searchProductWinplusByTap()">
              <i class="mdi mdi-lightbulb-on-outline"></i> Rechercher
            </button>
            </div>
            </div>
          </div>
        
        </ng-template>
        <kendo-grid-column field="codeGroupe" title="Code Groupe" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="codeWinplus" title="Code Winplus" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="designation" title="Designation" [width]="200"></kendo-grid-column>
        <kendo-grid-column field="prixAchatStd" title="PPH" [width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{dataItem.prixAchatStd | number: "1.2-2": "Fr-fr"}}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixVenteStd" title="PPV" [width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{dataItem.prixVenteStd | number: "1.2-2": "Fr-fr"}}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="codeBarre" title="Code Barre" class="text-end" [width]="100"></kendo-grid-column>
        <kendo-grid-column [width]="100" >
          <ng-template kendoGridCellTemplate let-dataItem>
              <button class="btn btn-primary btn-sm rounded-pill" (click)="updateProduitGroupeWithTranscodeWinplus(dataItem)">
                <i class="mdi mdi-link"></i>
              Transcoder
              </button>
          </ng-template>
        </kendo-grid-column>
    <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
        let-total="total">
        <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"  [allowPageSizes]="false"
          [navigation]="winplusProductNavigation" style="width: 100%;"
          (pageChange)="SearchProductWinplusPageChange($event)"></wph-grid-custom-pager>
      </ng-template>
        </kendo-grid>
  </div> 
</div>
 </ng-template>




<ng-template #updateProductDesigniation let-modal>
  <div class="modal-header">
    <h4 class="modal-title">
      <span>Modifier la désignation de produit  <span class="text-decoration-underline">{{this.clickedItem?.designation}}</span> </span>
    </h4>
    <button type="button" class="cross-button" (click)="modal.close()" >
      <i class="mdi mdi-close"></i>
    </button>
  </div>  <div class="modal-body">
    <form>
      <div class="mb-3">
        <label for="nom" class="form-label m-0">Désignation<span class="text-danger">*</span></label>
        <p class="text-muted m-0">La désignation doit ne pas dépasser 30 caractères.</p>
        <input type="text" class="form-control" id="nom" [(ngModel)]="productEditDesignation"[ngClass]="{'is-invalid': productEditDesignation?.trim() == '' || productEditDesignation?.length > 30}"
         name="productEditDesignation" placeholder="Modifier la désignation" 
         (keyup.enter)="updateProduitGroupeDesignation(modal)" appAutoFocus>
        <span class="text-danger" *ngIf="productEditDesignation?.trim() == ''">Ce champs est obligatoire</span>
        <span class="text-danger" *ngIf="productEditDesignation?.length >30">La désignation est trop long (30 caractères max)</span>

      </div>

      <!-- AI Assistant Suggestions Section -->
      <div class="mb-3" *ngIf="false">
        <h6 class="mb-2">Suggestions de désignation</h6>
        <div class="d-flex gap-2 mb-3">
          <button type="button" class="btn btn-outline-primary btn-sm" 
                  (click)="getWinplusDesignationSuggestions()" 
                  [disabled]="isLoadingDesignationSuggestions">
            <i class="mdi mdi-lightbulb-on-outline me-1"></i>
            <span *ngIf="!isLoadingDesignationSuggestions">Suggestions Assistées</span>
            <span *ngIf="isLoadingDesignationSuggestions">Chargement...</span>
          </button>
          <button type="button" class="btn btn-outline-secondary btn-sm" 
                  (click)="toggleWinplusDesignationSearch()" 
                  [class.active]="showWinplusDesignationSearch">
            <i class="mdi mdi-magnify me-1"></i>
            Recherche Manuelle
          </button>
        </div>

        <!-- AI Suggestions Results -->
        <div *ngIf="designationSuggestions?.length > 0" class="mb-3">
          <div class="card">
            <div class="card-header py-2">
              <small class="text-muted">Suggestions Assistées (Cliquez pour utiliser)</small>
            </div>
            <div class="card-body p-2">
              <div class="d-flex flex-wrap gap-1">
                <button type="button" 
                        class="btn btn-outline-info btn-sm" 
                        *ngFor="let suggestion of designationSuggestions"
                        (click)="useDesignationSuggestion(suggestion.designation)"
                        [title]="'Code: ' + suggestion.codeWinplus + ' | PPV: ' + (suggestion.prixVenteStd | number: '1.2-2')">
                  {{suggestion.designation}}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Manual Search Section -->
        <div *ngIf="showWinplusDesignationSearch" class="mb-3">
          <div class="card">
            <div class="card-header py-2">
              <small class="text-muted">Recherche manuelle de produits Winplus</small>
            </div>
            <div class="card-body p-2">
              <div class="row">
                <div class="col-md-8">
                  <input type="text" class="form-control form-control-sm" 
                         [(ngModel)]="winplusDesignationSearchModel.designation"
                         name="winplusDesignationSearch"
                         placeholder="Rechercher par désignation"
                         (keyup.enter)="searchWinplusForDesignation()">
                </div>
                <div class="col-md-4">
                  <button type="button" class="btn btn-primary btn-sm w-100" 
                          (click)="searchWinplusForDesignation()"
                          [disabled]="isLoadingWinplusDesignationSearch">
                    <i class="mdi mdi-magnify me-1"></i>
                    <span *ngIf="!isLoadingWinplusDesignationSearch">Rechercher</span>
                    <span *ngIf="isLoadingWinplusDesignationSearch">...</span>
                  </button>
                </div>
              </div>
              
              <!-- Search Results -->
              <div *ngIf="winplusDesignationSearchResults?.length > 0" class="mt-2" style="max-height: 200px; overflow-y: auto;scrollbar-width: thin;">
                <div class="list-group">
                  <button type="button" 
                          class="list-group-item list-group-item-action d-flex justify-content-between align-items-center py-1 px-2"
                          *ngFor="let result of winplusDesignationSearchResults"
                          (click)="useDesignationSuggestion(result.designation)">
                    <div>
                      <small class="fw-bold">{{result.designation}}</small>
                      <br>
                      <small class="text-muted">Code: {{result.codeWinplus}} | PPV: {{result.prixVenteStd | number: '1.2-2'}}</small>
                    </div>
                    <i class="mdi mdi-chevron-right"></i>
                  </button>
                </div>
              </div>
              
              <div *ngIf="winplusDesignationSearchResults?.length === 0 && !isLoadingWinplusDesignationSearch && winplusDesignationSearchModel.designation" 
                   class="mt-2 text-center text-muted">
                <small>Aucun résultat trouvé</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary"  (click)="modal.close()" >Annuler</button>
    <button type="button" class="btn btn-primary" (click)="updateProduitGroupeDesignation(modal)" 
    [disabled]="productEditDesignation?.trim() == '' || productEditDesignation?.length > 30"
    >
       Modifier
    </button>
  </div>
</ng-template>
