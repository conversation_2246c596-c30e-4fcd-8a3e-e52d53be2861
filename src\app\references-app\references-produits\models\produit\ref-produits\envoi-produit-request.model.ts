import { Noeud } from "./noeud.model";
import { ProduitSite } from "./produitSite.model";
import { ProduitWinplusDto } from "./produitsWinplusDto.model";

export interface EnvoiProduitRequest {
    ajout: boolean;
    listProduits: ProduitWinplusDto[];
    listSites: Noeud[];
  }

export interface ITracabiliteEnvoiProduit {
    listProduits: ProduitWinplusDto[];
    listSites: Noeud[];
}

export type TracabiliteEnvoiProduitResponse = { 
    produitSiteDto: ProduitSite;
    sites: Noeud[];
}