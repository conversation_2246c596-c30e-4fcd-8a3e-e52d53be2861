import { Component, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef, ViewChild, TemplateRef, ViewContainerRef } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CommandeService } from '../../../Services/achats/commande.service';
import { Subject } from 'rxjs';
import { PageChangeEvent, GridDataResult, GridComponent, RowClassArgs } from '@progress/kendo-angular-grid';

import { filter, takeUntil } from 'rxjs/operators';

import { Operateur } from '../../../models/common/operateur.model';
import { BaseRestorableComponent } from 'src/app/shared/base-restorable.component';
import { type } from 'os';
import { title } from 'process';

import { AuthService } from 'src/app/shared/services/auth.service';
import { Principal } from 'src/app/shared/models/principal';
import { log } from 'console';
import { AfterViewInit } from '@angular/core';
import moment from 'moment';
import { Pagination, PaginationAndSorting } from 'src/app/references-app/referential/models/pagination.interface';
import { Filter, Filters, FiltersModalBetaService } from 'src/app/shared/filters/filters-modal-service/filters-modal-beta.service';
import { ProduitSiteCriteria, ProduitSiteCriteriaForm } from '../../../models/produit/ref-produits/ProduitSiteCriteria.model';
import { UserInputService } from 'src/app/shared/services/user-input.service';
import { CommunService } from 'src/app/references-app/referential/services/commun.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { ProduitService } from '../../../Services/produit/produit.service';
import { ExportPdfService } from 'src/app/shared/export/export-pdf.service';
import { ProduitWinplusCriteria } from '../../../models/produit/ref-produits/produitsWinplus.model';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Noeud } from '../../../models/produit/ref-produits/noeud.model';
import { EnvoiProduitRequest } from '../../../models/produit/ref-produits/envoi-produit-request.model';
import { FournisseurService } from '../../../Services/tiers/fournisseur.service';
import { ProduitSite } from '../../../models/produit/ref-produits/produitSite.model';
@Component({
  selector: 'app-list-produits-envoie',
  templateUrl: './list-produits-envoie.component.html',
  styleUrls: ['./list-produits-envoie.component.scss']
})
export class ListProduitsEnvoieComponent implements OnInit {
  @ViewChild('secondGrid') secondGrid: GridComponent; 
  
  @ViewChild("outlet", { read: ViewContainerRef }) outletRef: ViewContainerRef;
  @ViewChild("sendSelectedGrid", { read: TemplateRef }) contentRef: TemplateRef<any>;

//   statutEnum = StatutCommandeEnum;
//   isLabo: boolean = false;
  destroy$: Subject<boolean> = new Subject<boolean>();

  listProduitsSite: ProduitSite[] = [];
 
  navigation: Pagination = {
    skip: 0,       // Offset for Kendo Grid
    pageSize: 20,  // Number of items per page
  };


get showDeselectAllButton(): boolean {
  return this.availableSites.length > 0 && this.selectedSites.length === this.availableSites.length;
}

get showClearButton(): boolean {
  return this.selectedSites.length > 0 && !this.showDeselectAllButton;
}


// Data for the first grid (Produits Winplus)
  gridData: GridDataResult = {
    data: [],
    total: 0
  };

  // Data for the second grid (Produits à envoyer)
  selectedGridData: GridDataResult = {
    data: [],
    total: 0
  };

  // Array to track selected items
  selectedItems: any[] = [];

  // Modal-related properties
  @ViewChild('configureEnvoiModal') configureEnvoiModal: TemplateRef<any>;
  @ViewChild('editDesignationsModal') editDesignationsModal: TemplateRef<any>;
  @ViewChild('productSummariesModal') productSummariesModal: TemplateRef<any>;
  selectedAction: string = 'Ajout'; // Default to 'Ajout'
  isModification: boolean = false; // Switch state
  isFilterOpen: boolean = false; // Filter modal state
  selectedSites: string[] = [];
  siteOptions: { value: string; label: string }[] = []; // Format for Select2
  filterForm:FormGroup;
  produitSiteCriteria: Partial<ProduitSiteCriteriaForm> = new ProduitSiteCriteriaForm({
    codeSite: [0] // Default to all sites
  });

  availableSites: Noeud[] = [];

  // Add new properties for editing designations
  productsToEdit: ProduitSite[] = [];
  editedDesignations: { [key: string]: string } = {};

  // Product summaries for modal display
  productSummaries: string[] = [];

  // Filter reference **
  envoiTypeControl = new FormControl(true);  

  filtersRef: Filters = null;
  filterCriteria: Partial<ProduitSiteCriteriaForm>  = {};
//   principal: Principal;



  constructor(private router: Router,  private alertService: AlertService, 
     private modalService: NgbModal, private noeudService: FournisseurService,
     private userInputService: UserInputService,
     private productsService: ProduitService,
    private fb: FormBuilder
    )  { }



  ngOnInit() {

   this.initFilterForm();

  }

  
  initFilterForm() {
    this.filterForm = this.fb.group({
      codeSite: [],
      codeProSite: [],
      designation: [null],
      codeWinplus: [null],
      codeGroupe: [null],
      dateCreateDu: [null],
      dateCreateAu: [null],
      dateDFPDu: [null],
      dateDFPAu: [null],
      codeBarre: [null],
      labelleLabo: [null],
      verified: [null],
      id: [null],
      transcoWinplus: [null],
      transcoCodeGroupe: [null],
      produitSupp: [null],
      codeSophatel:[null],
      envoyer:[null]

    });
  }


  openFilter() {
    this.isFilterOpen = true;
  }


  

  loadAllGrossistes(): void {
    this.productsService.getAllGrossiste().pipe(takeUntil(this.destroy$)).subscribe({
      next: (noeuds) => {
             this.availableSites = noeuds.filter(
              // exclude sites with code 53,50,52
              site => site.codeSite != 53 && site.codeSite != 50 && site.codeSite != 52
             ); // Filter out sites without code or name
          // Format sites for Select2
          this.siteOptions = noeuds.filter(
              // exclude sites with code 53,50,52
              site => site.codeSite != 53 && site.codeSite != 50 && site.codeSite != 52
             ).map(site => ({
            value: site.code,
            label: site.nom || site.code // Use name if available, else code
          }));
      },
    });
  }

  toggleSiteSelectionFromDropdown(event: any): void {
    this.selectedSites = event.value; // event.value is an array of selected codes
  }
   // Fetch available sites from the API
   // Fetch available sites and format for Select2
   loadAvailableSites(): void {
    this.noeudService.getFournisseurNoeudList()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (noeuds: Noeud[]) => {
          this.availableSites = noeuds.filter(site => site.code != ''); // Filter out sites without code or name
          // Format sites for Select2
          this.siteOptions = noeuds.filter(site => site.code != '').map(site => ({
            value: site.code,
            label: site.nom || site.code // Use name if available, else code
          }));
        }
      });
  }

  // Update selectedAction based on switch state
  updateAction(): void {
    this.selectedAction = this.envoiTypeControl?.value ;
  }

  loadListProduitsSite(): void {
    this.productsService.getProduitsSiteByCriteria(this.navigation , this.produitSiteCriteria).subscribe(
      (page) => {
        this.gridData = {
          data: page.content,
          total: page.totalElements
        };
      }
    );
  }



  clearFilter() {
      this.filterForm.reset();
      this.produitSiteCriteria ={ codeSite: [0] } 
      this.navigation.skip = 0;
      this.loadListProduitsSite();
      this.isFilterOpen = false;
    }
  
  
    filterSubmit() {
      this.isFilterOpen = false;
      const filterValues = this.filterForm.value;
      const criteria = this.filterToCriteria(filterValues);
      const cleanedCriteria = this.cleanEmptyValues(criteria);
      this.produitSiteCriteria = cleanedCriteria;
      this.navigation.skip = 0;
      this.loadListProduitsSite();
    }
  
    isValidateDate(date :string){
      return moment(date).isValid() && typeof date != 'undefined' && date != null
    }
  
  
  
    filterToCriteria(filterValues: any): ProduitSiteCriteriaForm {
      const criteria = new ProduitSiteCriteriaForm({
        designation: filterValues.designation,
        codeGroupe: filterValues.codeGroupe,
        codeProSite: filterValues.codeProSite,
        codeWinplus: filterValues.codeWinplus,
        transcoCodeGroupe: filterValues.transcoCodeGroupe,
        transcoWinplus: filterValues.transcoWinplus,
        produitSupp: filterValues.produitSupp,
        codeSite : [0],
        dateCreateDu: this.isValidateDate(filterValues.dateCreateDu) ? moment(filterValues.dateCreateDu).format('YYYY-MM-DD HH:mm:ss') : null,
        dateCreateAu: this.isValidateDate(filterValues.dateCreateAu) ? moment(filterValues.dateCreateAu).format('YYYY-MM-DD HH:mm:ss') : null,
        codeBarre: filterValues.codeBarre,
        labelleLabo: filterValues.labelleLabo,
        codeSophatel: filterValues.codeSophatel,
        envoyer: filterValues.envoyer,
      });
      return criteria;
    }
  
    cleanEmptyValues<T>(filterValues: T): T {
      const cleanedValues = { ...filterValues };
      Object.keys(cleanedValues).forEach(key => {
        if (cleanedValues[key] === null || cleanedValues[key] === undefined || typeof cleanedValues[key] === 'string' && cleanedValues[key].trim() === '') {
          delete cleanedValues[key];
        }
      });
      return cleanedValues;
    }
  



//   /* -------------------------------------------------------------------------- */
//   /*                 selection process                                          */
//   /* -------------------------------------------------------------------------- */

// Check if an item is selected in the first grid
  isSelected(item: any): boolean {
    return this.selectedItems.some(selected => selected === item);
  }

  isSiteSelected(site: Noeud): boolean {
    return this.selectedSites.includes(site.code); 
  }
  private mapSitesToNoeuds(selectedSiteCodes: string[]): Noeud[] {
    return this.availableSites.filter(site => selectedSiteCodes.includes(site.code));
  }
  // Toggle selection in the first grid
  toggleSelection(item: any): void {
    if (this.isSelected(item)) {
      this.selectedItems = this.selectedItems.filter(selected => selected !== item);
    } else {
      this.selectedItems.push(item);
    }
    this.updateSelectedGrid();
  }

  // Check if an item is selected in the second grid
  isSelectedInSecondGrid(item: any): boolean {
    return this.selectedItems.some(selected => selected.codeWinplus === item.codeWinplus);
  }

  // Toggle selection in the second grid (only removes items)
  toggleSelectionInSecondGrid(event: Event, item: any): void {
    event.stopPropagation(); // Prevent the event from propagating to the next row
  
    const index = this.selectedItems.findIndex(selected => selected.codeWinplus === item.codeWinplus);
    if (index > -1) {
      this.selectedItems.splice(index, 1);
    }
    this.outletRef.clear();
    this.outletRef.createEmbeddedView(this.contentRef);
    this.updateSelectedGrid();
  }

  // Select all items in the first grid
  selectAll(event: any): void {
    if (event.target.checked) {
      this.selectedItems = [...this.gridData.data];
    } else {
      this.selectedItems = [];
    }
    this.updateSelectedGrid();
  }

  // Deselect all items in the second grid
  selectAllSelected(event: any): void {
    if (!event.target.checked) {
      this.selectedItems = [];
      this.updateSelectedGrid();
    }
  }

  // Update the second grid with selected items
  updateSelectedGrid(): void {
    this.selectedGridData = {
      data: [...this.selectedItems],
      total: this.selectedItems.length
    };
  }
// Toggle site selection for checkboxes
toggleSiteSelection(site: Noeud): void {
  const index = this.selectedSites.indexOf(site.code); 
  if (index > -1) {
    this.selectedSites.splice(index, 1);
  } else {
    this.selectedSites.push(site.code);
  }
}
selectAllSites(): void {
  if(this.showDeselectAllButton){ // If all are selected, deselect them
    this.deselectAllSites();
    return;
  }
  this.selectedSites = this.availableSites.map(site => site.code);
}

deselectAllSites(): void {
  this.selectedSites = []; // Clear the selected sites array
}

trackByCodeWinplus(index: number, item: any): string {
  return item.codeWinplus + '-' + index;
}

  // Handle modal confirmation
  confirmEnvoi(modal: any): void {
    if (this.selectedAction && this.selectedSites.length > 0) {
      const request: EnvoiProduitRequest = {
        ajout: this.envoiTypeControl.value, 
        listProduits: this.selectedItems,
        listSites: this.mapSitesToNoeuds(this.selectedSites)
      };
      modal.close(request);
    }
  }

  checkProductHaveDesignationBiggerThan30Chars(): boolean {
    return this.selectedItems.some(item => item.designation?.length > 30);
  }

  // Get products with long designations
  getProductsWithLongDesignations(): ProduitSite[] {
    return this.selectedItems.filter(item => item.designation?.length > 30);
  }
  
  // Initialize the editing process
  initDesignationEditing(): void {
    this.productsToEdit = this.getProductsWithLongDesignations();
    this.editedDesignations = {};
    
    // Pre-populate the edited designations with current values
    this.productsToEdit.forEach(product => {
      this.editedDesignations[product.codeGroupe] = product.designation;
    });
  }
  
  // Update a single designation
  updateDesignation(codeGroupe: string, newDesignation:any): void {
    this.editedDesignations[codeGroupe] = newDesignation.target.value;
  }
  
  // Check if all edited designations are valid (less than 31 chars)
  areAllDesignationsValid(): boolean {
    return Object.values(this.editedDesignations).every(designation => 
      designation && designation.length <= 30);
  }
  
  // Save edited designations to the selected items
  saveDesignations(modal: any): void {
    if (!this.areAllDesignationsValid()) {
      this.alertService.warning("Toutes les désignations doivent comporter 30 caractères ou moins.");
      return;
    }
    
    // Update the selected items with new designations
    this.selectedItems.forEach(item => {
      if (this.editedDesignations[item.codeGroupe]) {
        item.designation = this.editedDesignations[item.codeGroupe];
        this.updateProductGroupeDesignation(item); // Update the product group with the new designation
      }
    }
    );
    
    // Update the second grid display
    this.updateSelectedGrid();
    
    // Close the modal and continue with sending
    modal.close();
    this.openEnvoiConfigModal();
  }
  
  // Show character count for the input field
  getCharCount(text: string): string {
    return text ? `${text.length}/30` : '0/30';
  }

  checktracabilityBeforSend() {
    if (this.selectedItems.length === 0) {
      this.alertService.warning("Veuillez sélectionner au moins un produit à envoyer.");
      return;
    }
    const envoiRequest: EnvoiProduitRequest = {
      ajout: this.envoiTypeControl.value,
      listProduits: this.selectedItems,
      listSites: this.mapSitesToNoeuds(this.selectedSites)
    };
    this.productsService.checkTracabiliteProduit(envoiRequest).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (response) => {
        if (response && response.length > 0) {
          // Products already sent to some sites
          const productSummaries = response.filter(item => item.sites.length > 0).map(item => {
            const productName = item.produitSiteDto.designation || `Code: ${item.produitSiteDto.codeGroupe}`;
            const siteNames = item.sites.map(site => site.nom || site.code).join(', ');
            return `"${productName}" déjà envoyé à: ${siteNames}`;
          });
          
          if(productSummaries && productSummaries.length > 0){
            this.productSummaries = productSummaries;
            this.openProductSummariesModal();
          }else{
            this.sendProductToBackend(envoiRequest);
          }
          
        }
      },
    });

  }

  sendProductToBackend(envoieRequest : EnvoiProduitRequest){
    this.productsService.envoieProduitWinplusToSite(envoieRequest).subscribe({
      next: () => {
        this.alertService.success("Les produits ont été envoyés aux sites avec succès");
        this.selectedItems = [];
        this.updateSelectedGrid();
      }
    });
  }
  
  // Open the configuration modal for sending
  openEnvoiConfigModal(): void {
    this.selectedAction = 'Ajout'; 
    this.isModification = false; 
    this.selectedSites = [];
    this.loadAllGrossistes();
    
    const modalRef = this.modalService.open(this.configureEnvoiModal);
    modalRef.result.then((result: EnvoiProduitRequest) => {
      if (result) {
        this.userInputService.confirm(
          "Confirmer l'envoi des produits",                        
          "Voulez-vous vraiment envoyer les produits sélectionnés aux sites choisis ?",   
          "Envoyer",                                              
          "Annuler"                                            
        ).then(confirmed => {
          if (confirmed) {
            
            if(result.ajout){
              this.checktracabilityBeforSend();
            }else{
            this.sendProductToBackend(result);
            }
          }
        }).catch(() => {
          console.log('Confirmation dismissed');
        });
      }
    }).catch(() => {
      console.log('Configure modal dismissed');
    });
  }
  
  // Open the product summaries modal
  openProductSummariesModal(): void {
    const modalRef = this.modalService.open(this.productSummariesModal, { size: 'lg' });
    modalRef.result.then(() => {
      // Modal closed with OK
    }).catch(() => {
      // Modal dismissed
    });
  }

  // Update the sendSelected method to check designations first
  sendSelected(): void {
    if (this.getProductsWithLongDesignations().length > 0) {
      this.initDesignationEditing();
      this.modalService.open(this.editDesignationsModal, { size: 'lg' });
    } else {
      this.openEnvoiConfigModal();
    }
  }
  
//   /* -------------------------------------------------------------------------- */
//   /*                              pagination event                              */
//   /* -------------------------------------------------------------------------- */
pageChange(skip: number): void {
    this.navigation.skip = skip;
    this.loadListProduitsSite();
}

  hasCriteria(filter: Partial<ProduitSiteCriteriaForm>): boolean {
    return Object.keys(filter ?? {}).length > 0;
  }

  



 



//   /* -------------------------------------------------------------------------- */
//   /*                          SECTION:: EXPORT SECTION                          */
//   /* -------------------------------------------------------------------------- */
//   exportRef: ExportPdf<EnteteCmdAchat>;

//   initExport() {
//     this.exportRef = this.exportPdfService
//       .ref<EnteteCmdAchat>()
//       .setFiltersRef(this.filtersRef)
//       .setTitle('Liste des commandes')
//       .setTableName("Table des Commandes")
//       .addColumn('dateCmd', 'Date Cmd.', { type: 'date', width: 55 })
//       .addColumn('numCmd', 'N° cmd.', {
//         type: "numero",
//         width: 40,
//         transform: (value) => {
//           return value > 0 ? value : null
//         }
//       })
//       .addColumn<Operateur>('operateur', 'Operateur', { keyAccessor: "code", width: 50 })
//       .addColumn('fournisseur', 'Fournisseur', { keyAccessor: ["raisonSociale"] })
//       // .addColumn('totalQtCmd', 'Qu antité Commandée', { type: 'integer' })
//       .addColumn('mntBrutTtc', 'Mnt cmd.', { type: 'decimal', width: 50 })
//       .addColumn('statut', 'Statut', {
//         width: 52,
//         transform: (value) => {
//           return this.statutPipe.transform(value)
//         }
//       })
//     // this.exportRef.setDataObservableFunc(this.commandeServ.listCommandes.bind(this.commandeServ));


//   }


//   fetchBlAndNavigate(commandeId: number): void {
//     this.blService.getAllEnteteBlByCmd(commandeId).subscribe(
//       (bls) => {
//         if (bls && bls.length > 0) {
//           // Navigate to the reception page if BLs are available
//           this.router.navigate(
//             ['/webfix/achat/receptionbl'],
//             { queryParams: { commandeId, returnUrl: this.router.url } }
//           );
//         } else {
//           this.alertServ.warning(
//             "Aucun BL disponible",
//             "Il n'y a aucun bon de livraison disponible pour cette commande."
//           );
//         }
//       }
//     );
//   }








  compareFn(a, b) {
    return a == b;
  }



  rowClass(rowClass:RowClassArgs){
    if((rowClass.dataItem as ProduitSite).designation?.length > 30){
      return "k-grid-row-long-text";
    }
    return "";
  }


  private updateProductGroupeDesignation(product: ProduitSite) {
    product.designation = this.editedDesignations[product.codeGroupe] || product.designation;
    this.productsService.updateOrCreateProduitGroupe(product).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (updatedProduct) => {
        const index = this.selectedItems.findIndex(item => item.codeGroupe === updatedProduct.codeGroupe);
        if (index > -1) {
          this.selectedItems[index] = updatedProduct; // Update the selected item with the new designation
          // this.updateSelectedGrid(); // Refresh the second grid
        }
      }
    });
  }


  ngOnDestroy(): void {
    ///console.log("::: Destroy Comp ")
    this.destroy$.next(true);
    this.destroy$.unsubscribe();
  }





}
