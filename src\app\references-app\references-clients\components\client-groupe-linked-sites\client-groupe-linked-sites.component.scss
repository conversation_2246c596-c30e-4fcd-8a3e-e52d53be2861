// Custom styles for client groupe linked sites component

// Compact grid styles
.compact-grid {
  .k-grid-header {
    th {
      padding: 8px 6px !important;
      font-size: 0.85rem !important;
      font-weight: 600 !important;
    }
  }
  
  .k-grid-content {
    td {
      padding: 6px 6px !important;
      font-size: 0.85rem !important;
      line-height: 1.2 !important;
    }
  }
  
  .k-grid-toolbar {
    padding: 0 !important;
    min-height: 32px !important;
  }
  
  .k-pager-wrap {
    padding: 8px !important;
  }
}

// Compact tabs
.compact-tabs {
  .nav-link {
    padding: 8px 12px !important;
    font-size: 0.9rem !important;
    
    .mdi {
      font-size: 1rem !important;
    }
    
    .badge {
      font-size: 0.7rem !important;
      padding: 2px 6px !important;
    }
  }
}

// Statistics Cards
.stats-card {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--accent-color);
    transition: all 0.3s ease;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  }
  
  &.total-platforms {
    --accent-color: #6c5ce7;
    .stats-icon {
      background: linear-gradient(135deg, #6c5ce7, #a29bfe);
    }
  }
  
  &.used-platforms {
    --accent-color: #00b894;
    .stats-icon {
      background: linear-gradient(135deg, #00b894, #00cec9);
    }
  }
  
  &.unused-platforms {
    --accent-color: #fdcb6e;
    .stats-icon {
      background: linear-gradient(135deg, #fdcb6e, #e17055);
    }
  }
  
  .stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    
    .mdi {
      font-size: 24px;
      color: white;
    }
  }
  
  .stats-content {
    flex: 1;
    
    .stats-number {
      font-size: 2rem;
      font-weight: 700;
      margin: 0;
      color: #2d3436;
      line-height: 1;
    }
    
    .stats-label {
      font-size: 0.9rem;
      color: #636e72;
      margin: 0;
      font-weight: 500;
    }
  }
}

// Platforms Section
.platforms-container {
  .platforms-header {
    .platforms-title {
      color: #2d3436;
      font-weight: 600;
      margin: 0;
      font-size: 1.1rem;
    }
  }
}

// Platform Grid
.platforms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  padding: 0;
}

// Platform Item
.platform-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 4px;
    background: var(--status-color);
    transition: all 0.3s ease;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: var(--status-color);
  }
  
  &.platform-active {
    --status-color: #00b894;
    background: linear-gradient(135deg, #fff 0%, #f0fff4 100%);
    
    &:hover {
      background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
    }
  }
  
  &.platform-inactive {
    --status-color: #ddd;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    
    .platform-content {
      opacity: 0.7;
    }
    
    &:hover {
      background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
    }
  }
  
  .platform-status-indicator {
    margin-right: 15px;
    
    .status-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      position: relative;
      
      &.active {
        background: #00b894;
        box-shadow: 0 0 0 3px rgba(0, 184, 148, 0.2);
        
        &::before {
          content: '';
          position: absolute;
          width: 6px;
          height: 6px;
          background: white;
          border-radius: 50%;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
      
      &.inactive {
        background: #ddd;
        border: 2px solid white;
      }
    }
  }
  
  .platform-content {
    flex: 1;
    
    .platform-header {
      display: flex;
      align-items: center;
      
      .platform-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #6c5ce7, #a29bfe);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        .mdi {
          font-size: 20px;
          color: white;
        }
      }
      
      .platform-info {
        flex: 1;
        
        .platform-name {
          font-size: 1rem;
          font-weight: 600;
          margin: 0 0 2px 0;
          color: #2d3436;
          line-height: 1.2;
        }
        
        .platform-status {
          font-size: 0.8rem;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          
          &.status-active {
            color: #00b894;
          }
          
          &.status-inactive {
            color: #636e72;
          }
        }
      }
    }
  }
  
  .platform-actions {
    .status-badge {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      
      .mdi {
        font-size: 16px;
        color: white;
      }
      
      &.badge-success {
        background: linear-gradient(135deg, #00b894, #00cec9);
        
        &:hover {
          transform: scale(1.1);
        }
      }
      
      &.badge-secondary {
        background: linear-gradient(135deg, #b2bec3, #636e72);
        
        &:hover {
          transform: scale(1.1);
        }
      }
    }
  }
}

// Empty State
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #636e72;
  
  .empty-icon {
    margin-bottom: 20px;
    
    .mdi {
      font-size: 4rem;
      color: #ddd;
    }
  }
  
  .empty-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2d3436;
  }
  
  .empty-message {
    font-size: 0.9rem;
    margin: 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.5;
  }
}

// Tab content spacing
.tab-content-wrapper {
  padding: 0 !important;
}

// Action icons
.circle-sm {
  width: 24px !important;
  height: 24px !important;
  
  .mdi {
    font-size: 14px !important;
  }
}

// Loading and empty states
.no-transco-card {
  min-height: 150px !important;
  
  .fs-2 {
    font-size: 2rem !important;
  }
  
  p {
    font-size: 0.9rem !important;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .compact-grid {
    .k-grid-header th,
    .k-grid-content td {
      padding: 4px !important;
      font-size: 0.8rem !important;
    }
  }
  
  .platforms-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .platform-item {
    padding: 16px;
    
    .platform-content .platform-header .platform-icon {
      width: 36px;
      height: 36px;
      
      .mdi {
        font-size: 18px;
      }
    }
  }
  
  .stats-card {
    padding: 16px;
    
    .stats-icon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      
      .mdi {
        font-size: 20px;
      }
    }
    
    .stats-content .stats-number {
      font-size: 1.6rem;
    }
  }
  
  .compact-tabs .nav-link {
    padding: 6px 8px !important;
    font-size: 0.85rem !important;
  }
}


.nav-link:hover, .nav-link:focus {
  text-decoration: none !important;
  color: #000 !important;
}
.nav-link.active {
  color: #000 !important;
  font-weight: 600 !important;
}