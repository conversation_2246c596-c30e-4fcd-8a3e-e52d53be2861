import { WinClientVille } from "./ville";

interface IClientGroupe { 
  id?:number;
  adresse1: string;
  adresse2: string;
  codeClientGroupe: string;
  ice: string;
  localite: string;
  nomPharmacien: string;
  patente: string;
  raisonSociale: string;
  tag: string;
  telephone: string;
  gsm: string;
  whatsapp: string;
  telephone2: string;
  email:string;
  ville: WinClientVille;
  inpe: string;
  longitude?: number;
  latitude?: number;
  classification: string;
  segment: ClientGroupeSegment;
}


interface IClientGroupeCriteria {
  codeClientGroupe: string;
  nomPharmacien: string;
  page: number;  
  raisonSociale: string;
  size: number;   
  tag: string;
  villeLibelle: string;
  adress: string;
  // whatsapp: string;
  // telephone2: string;
  telephone: string;
  // gsm: string;
  classification: string;
  segment: ClientGroupeSegment;
  estUtilise: boolean | null;
}


export enum ClientGroupeSegment {
  PHARMACIE = "PHARMACIE",
  PARAPHARMACIE = "PARAPHARMACIE",
  CENTRE_BEAUTE = "CENTRE_BEAUTE"
}


export class ClientGroupeCriteria implements IClientGroupeCriteria{
    codeClientGroupe: string;
    nomPharmacien: string;
    page: number = 0;   
    raisonSociale: string;
    size: number;   
    tag: string;
    villeLibelle: string;
    adress: string;
    // whatsapp: string;
    // telephone2: string;
    telephone: string;
    // gsm: string;
    classification: string;
    segment: ClientGroupeSegment;
    estUtilise: boolean | null;

    
    constructor(clientGroupeCriteria:Partial<IClientGroupeCriteria>){
        Object.assign(this,clientGroupeCriteria);
    }
}





export class ClientGroupe implements IClientGroupe{
    id?: number;
    adresse1: string;
    adresse2: string;
    codeClientGroupe: string;
    ice: string;
    localite: string;
    nomPharmacien: string;
    patente: string;
    raisonSociale: string;
    tag: string;
    telephone: string;
    gsm: string;
    whatsapp: string;
    telephone2: string;
    email:string;
    ville: WinClientVille;
    inpe: string;
    longitude?: number;
    latitude?: number;
    classification: string;
    segment: ClientGroupeSegment;


    constructor(clientGroupe:Partial<IClientGroupe>){
        Object.assign(this,clientGroupe);
    }

}