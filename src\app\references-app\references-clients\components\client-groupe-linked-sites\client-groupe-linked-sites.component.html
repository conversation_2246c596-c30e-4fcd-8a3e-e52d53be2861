<div class="px-1 py-1" style="display: flex; flex-direction: column; height: 100%; overflow-y: auto; scrollbar-width: thin;">
  <!-- Client Groupe Info Grid -->
  <kendo-grid [kendoGridBinding]="[clientGroupe]"
    class="content-wrap flex-shrink-0 ref-grid compact-grid" style="height:auto !important;">
    <ng-template kendoGridToolbarTemplate>
      <div style="height: 32px;" class="d-flex justify-content-between align-items-center px-2 client-have-no-association-bg">
        <span class="text-white fs-5 k-font-weight-bold">{{clientGroupeGridTitle}}</span>
      </div>
    </ng-template>
    <kendo-grid-column field="codeSite" title="" [width]="70">
    </kendo-grid-column>
    <kendo-grid-column field="codeGroupe" title="Code Groupe" [width]="80">
      <ng-template kendoGridCellTemplate let-dataItem>
        <app-copy-cell [value]="dataItem?.codeGroupe"></app-copy-cell>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="raisonSociale" title="Raison Sociale" [width]="180">
      <ng-template kendoGridCellTemplate let-dataItem>
        <app-copy-cell [value]="dataItem?.raisonSociale"></app-copy-cell>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="nomPharmacien" title="Nom Pharmacien" [width]="150">
      <ng-template kendoGridCellTemplate let-dataItem>
        <app-copy-cell [value]="dataItem?.nomPharmacien"></app-copy-cell>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="ville.libelle" title="Ville" [width]="80"></kendo-grid-column>
    <kendo-grid-column field="adresse1" title="Adresse" [width]="150"></kendo-grid-column>
    <kendo-grid-column field="localite" title="Localité" [width]="80"></kendo-grid-column>
    <kendo-grid-column field="telephone" title="Téléphone" [width]="90">
      <ng-template kendoGridCellTemplate let-dataItem>
        <app-copy-cell [value]="dataItem?.telephone"></app-copy-cell>
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
  <!-- Tab Navigation -->
  <div *ngIf="!isLoadingLinkedClientSites" class="mt-2">
    <ul ngbNav #tabNav="ngbNav" [(activeId)]="activeTab" class="nav-tabs nav-bordered compact-tabs">
      <li [ngbNavItem]="1">
        <a ngbNavLink class="px-3 py-2">
          <i class="mdi mdi-link-variant me-1"></i>
          <span>Sites Liés</span>
          <span class="badge bg-primary ms-1" *ngIf="linkedClientSites.total > 0">
            {{linkedClientSites.total}}
          </span>
        </a>
        <ng-template ngbNavContent>
          <!-- Linked Client Sites Content -->
          <div class="tab-content-wrapper">
            <!-- List Client Sites -->
            <kendo-grid [data]="linkedClientSites" style="height: calc(100vh - 320px);"
              [pageable]="true"
              [pageSize]="linkedClientSitesNavigation.pageSize"
              [skip]="linkedClientSitesNavigation.skip"
              [sortable]="true"
              [sort]="linkedClientSitesSort"
              (sortChange)="linkedClientSitesSortChange($event)"
              class="content-wrap flex-shrink-0 client-have-association-grid custom-sort-grid compact-grid"  
              *ngIf="linkedClientSites.data.length > 0 && !isLoadingLinkedClientSites">
              <ng-template kendoGridToolbarTemplate>
                <div style="height: 32px;" class="d-flex justify-content-between align-items-center px-2 client-have-association-bg">
                  <span class="text-white fs-5 k-font-weight-bold">Sites Associés</span>
                </div>
              </ng-template>
              <kendo-grid-column field="codeSite" title="Site" [width]="60">
                 <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
                  <app-grid-sort-header [title]="column.title" [type]="'numeric'"
                  [active]="linkedClientSitesNavigation.sortField === column.field"
                  [direction]="linkedClientSitesNavigation.sortMethod"></app-grid-sort-header>
            </ng-template>
                <ng-template kendoGridCellTemplate let-dataItem>
                  <span [ngClass]="{'text-success font-weight-bold': dataItem?.estActifSite}">
                    {{dataItem?.codeSite | clientSitePipe}}
                  </span>
                 </ng-template>
              </kendo-grid-column>
              <kendo-grid-column field="cliCode" title="Code" [width]="70">
                 <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
                  <app-grid-sort-header [title]="column.title" [type]="'numeric'"
                  [active]="linkedClientSitesNavigation.sortField === column.field"
                  [direction]="linkedClientSitesNavigation.sortMethod"></app-grid-sort-header>
              </ng-template>
                <ng-template kendoGridCellTemplate let-dataItem>
                  <app-copy-cell [value]="dataItem?.cliCode"></app-copy-cell>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column field="cliRaiSoc" title="Raison Sociale" [width]="160">
                 <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
                  <app-grid-sort-header [title]="column.title"  
                  [active]="linkedClientSitesNavigation.sortField === column.field"
                  [direction]="linkedClientSitesNavigation.sortMethod"></app-grid-sort-header>
               </ng-template>
                <ng-template kendoGridCellTemplate let-dataItem>
                  <app-copy-cell [value]="dataItem?.cliRaiSoc"></app-copy-cell>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column field="cliNomPhar" title="Nom Pharmacien" [width]="130">
                 <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
                  <app-grid-sort-header [title]="column.title"  
                  [active]="linkedClientSitesNavigation.sortField === column.field"
                  [direction]="linkedClientSitesNavigation.sortMethod"></app-grid-sort-header>
                </ng-template>
                <ng-template kendoGridCellTemplate let-dataItem>
                  <app-copy-cell [value]="dataItem?.cliNomPhar"></app-copy-cell>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column field="cliVille" title="Ville" [width]="80" [sortable]="false"></kendo-grid-column>
              <kendo-grid-column field="cliAdresse" title="Adresse" [sortable]="false" [width]="140"></kendo-grid-column>
              <kendo-grid-column field="cliLocalite" title="Localité" [width]="80">
                  <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
                  <app-grid-sort-header [title]="column.title"  
                  [active]="linkedClientSitesNavigation.sortField === column.field"
                  [direction]="linkedClientSitesNavigation.sortMethod"></app-grid-sort-header>
            </ng-template>
              </kendo-grid-column>
              <kendo-grid-column field="telephone" title="Téléphone" [width]="90"  [sortable]="false">
                <ng-template kendoGridCellTemplate let-dataItem>
                  <app-copy-cell [value]="dataItem?.telephone"></app-copy-cell>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column title="Actions" [width]="80">
                <ng-template kendoGridCellTemplate let-dataItem>
                  <button class="btn btn-sm btn-danger rounded-pill d-flex justify-content-center align-items-center k-gap-1"  (click)="detacherClientSite(dataItem)">
                    <i class="mdi mdi-close"></i>
                    <span class="d-none d-md-inline">Détacher</span>
                  </button>
                 </ng-template>
              </kendo-grid-column>
              <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage" let-total="total">
                <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage" [allowPageSizes]="false"
                  [navigation]="linkedClientSitesNavigation" style="width: 100%;"
                  (pageChange)="linkedClientSitesPageChange($event)"></wph-grid-custom-pager>
              </ng-template>
            </kendo-grid>            <!-- loading state -->
            <div class="card flex-grow-0 card-body no-transco-card d-flex flex-column justify-content-center align-items-center py-3" *ngIf="isLoadingLinkedClientSites">
              <div class="d-flex justify-content-center align-items-center">
                <i class="mdi mdi-loading mdi-spin text-primary fs-2"></i>
              </div>
              <p class="font-weight-600 m-0 mt-2">Chargement des Sites Liés...</p>
            </div>
            
            <!-- no data found -->
            <div class="card card-body flex-grow-0 no-transco-card d-flex flex-column justify-content-center align-items-center py-3" *ngIf="!isLoadingLinkedClientSites && linkedClientSites.data.length === 0">
              <div class="d-flex justify-content-center align-items-center">
                <i class="mdi mdi-alert-circle-outline text-warning fs-2"></i>
              </div>
              <p class="font-weight-600 m-0 mt-2">Ce client n'a pas de sites associés.</p>
            </div>
          </div>
        </ng-template>
      </li>

      <li [ngbNavItem]="2">
        <a ngbNavLink class="px-3 py-2">
          <i class="mdi mdi-server-network me-1"></i>
          <span>Plateformes</span>
          <span class="badge bg-success ms-1" *ngIf="clientGroupePlatforms?.plateformes?.length > 0">
            {{clientGroupePlatforms?.plateformes?.length}}
          </span>
        </a>        <ng-template ngbNavContent>
          <!-- Platform Usage Content -->
          <div class="tab-content-wrapper">
            <!-- Usage Summary Cards -->
            <div class="row mb-3" *ngIf="false">
              <div class="col-md-4">
                <div class="stats-card total-platforms">
                  <div class="stats-icon">
                    <i class="mdi mdi-server-network"></i>
                  </div>
                  <div class="stats-content">
                    <h4 class="stats-number">{{allPlatforms.length}}</h4>
                    <p class="stats-label">Total Plateformes</p>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="stats-card used-platforms">
                  <div class="stats-icon">
                    <i class="mdi mdi-check-circle"></i>
                  </div>
                  <div class="stats-content">
                    <h4 class="stats-number">{{clientGroupePlatforms?.plateformes?.length || 0}}</h4>
                    <p class="stats-label">Utilisées</p>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="stats-card unused-platforms">
                  <div class="stats-icon">
                    <i class="mdi mdi-close-circle"></i>
                  </div>
                  <div class="stats-content">
                    <h4 class="stats-number">{{allPlatforms.length - (clientGroupePlatforms?.plateformes?.length || 0)}}</h4>
                    <p class="stats-label">Non Utilisées</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Platforms List -->
            <div class="platforms-container">
              <div class="platforms-header mb-3" *ngIf="IsHaveAtLeastOnePlatform">
                <h6 class="platforms-title mt-4">
                  <i class="mdi mdi-view-grid me-2"></i>
                   Client Groupe ({{clientGroupe?.raisonSociale}}) utilise les plateformes suivantes :
                </h6>
              </div>

              <div class="platforms-grid" *ngIf="allPlatforms.length > 0 && IsHaveAtLeastOnePlatform; else noPlatforms">
                <ng-container *ngFor="let platform of allPlatforms">
                  <div class="platform-item" *ngIf="isPlatformUsed(platform.libelle)"  [ngClass]="{'platform-active': isPlatformUsed(platform.libelle), 'platform-inactive': !isPlatformUsed(platform.libelle)}">
                  
                    <div class="platform-status-indicator">
                      <div class="status-dot" [ngClass]="isPlatformUsed(platform.libelle) ? 'active' : 'inactive'"></div>
                    </div>
                  
                    <div class="platform-content">
                      <div class="platform-header">
                        <div class="platform-icon">
                          <i class="mdi mdi-server-network"></i>
                        </div>
                        <div class="platform-info">
                          <h6 class="platform-name">{{platform.libelle}}</h6>
                          <span class="platform-status"
                                [ngClass]="isPlatformUsed(platform.libelle) ? 'status-active' : 'status-inactive'">
                            {{isPlatformUsed(platform.libelle) ? 'Actif' : 'Inactif'}}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="platform-actions">
                      <div class="status-badge"
                           [ngClass]="isPlatformUsed(platform.libelle) ? 'badge-success' : 'badge-secondary'">
                        <i class="mdi"
                           [ngClass]="isPlatformUsed(platform.libelle) ? 'mdi-check' : 'mdi-minus'"></i>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>

              <ng-template #noPlatforms>
                <div class="empty-state">
                  <div class="empty-icon">
                    <i class="mdi mdi-server-off"></i>
                  </div>
                  <h6 class="empty-title">Aucune plateforme disponible</h6>
                  <p class="empty-message">Il n'y a actuellement aucune plateforme utilisée par {{clientGroupe?.raisonSociale}}.</p>
                </div>
              </ng-template>
            </div>

            <!-- Platform Loading State -->
            <div class="card flex-grow-0 card-body d-flex flex-column justify-content-center align-items-center py-3" *ngIf="isLoadingPlatforms">
              <div class="d-flex justify-content-center align-items-center">
                <i class="mdi mdi-loading mdi-spin text-primary fs-2"></i>
              </div>
              <p class="font-weight-600 m-0 mt-2">Chargement des plateformes...</p>
            </div>
          </div>
        </ng-template>
      </li>
    </ul>
    <div [ngbNavOutlet]="tabNav" class="mt-2"></div>
  </div>


     <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="isLoadingLinkedClientSites">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-loading mdi-spin text-primary fs-1"></i>
        </div>
        <p class="font-weight-600 m-0">Chargement des informations......</p>
      </div>

</div>
