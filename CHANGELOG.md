# Git Changelog

Generated on: 2025-07-22T13:23:10.124Z

# Changelog

## v1.1.7
**Released:** 2025-07-22 14:21:08 +0100

- feat: enhance product editing modal with additional fields for pricing and barcode (fcd519e)
- fix: update titles and field mappings in diff-prix-winplus component for clarity and consistency (9c05de4)
- chore: release 1.1.6 by (<PERSON><PERSON><PERSON>) (dfb9d68)

## v1.1.6
**Released:** 2025-07-21 14:02:10 +0100

- feat: implement transco drawer functionality with data loading and row highlighting (3a484dd)
- fix: correct media query indentation for custom drawer component (0750f49)
- fix: initialize sites as an empty array and add a check for empty response in getAllAvailableSites (b90e5ae)
- feat: update styling for active sales in Kendo grid to improve visibility (dc3e4bd)
- refactor: remove console logs from AuthGuardService for cleaner code (09f4353)
- feat: add conditional styling for active sales in Kendo grid (892b8bc)
- chore: release 1.1.5 by (<PERSON><PERSON><PERSON>) (d1b0852)

## v1.1.5
**Released:** 2025-07-18 14:57:15 +0100

- feat: add sortable 'Priorite Vente' column to Diff Prix Winplus grid (4c9534a)
- feat: enhance display of codeSite in Kendo grid by adding conditional styling for active sales (a40b089)
- feat: enhance filter functionality in Diff Prix Winplus component by adding designation fields and updating sorting logic (4af1f2e)
- chore: release 1.1.4 by (Hicham Dadda) (6459310)

## v1.1.4
**Released:** 2025-07-17 11:56:38 +0100

- feat: add filter functionality to Diff Prix Winplus component with custom drawer (c0951a7)
- fix: enable sorting for Diff Prix Winplus grid and update sorting logic (fce488d)
- chore: release 1.1.3 by (Hicham Dadda) (45ecff8)

## v1.1.3
**Released:** 2025-07-16 16:09:57 +0100

- fix: add optional chaining to prevent errors when clearing custom select state (f2a4d69)
- chore: release 1.1.2 by (Hicham Dadda) (943cc66)

## v1.1.2
**Released:** 2025-07-15 12:27:33 +0100

- chore: release 1.1.1 by (Hicham Dadda) (d31d1fb)

## v1.1.1
**Released:** 2025-07-15 12:26:47 +0100

- feat: add AppVersionInterceptor to include client version in HTTP headers (353083e)
- feat: add tooltip to action icon for updating Winplus price (148980a)
- fix: increase maximum error size limit in angular.json to 8mb in dev env (f44b2d3)
- feat: add width property to username column for improved grid layout (7216a0e)
- chore: release 1.1.0 by (Hicham Dadda) (79d814a)

## v1.1.0
**Released:** 2025-07-10 14:22:00 +0100

- feat: enhance user grid layout by adding width properties to columns for better responsiveness (b7b1238)
- feat: improve layout and responsiveness of dashboard components (84fea90)
- feat: update grid layout for client group form to use responsive column classes (b8b9653)
- feat: add responsive width for custom drawer on mobile devices (2457169)
- feat: clean up topbar component by removing unused HTML elements and improving user authority display (2e17616)
- feat: update routing to enforce role-based access for SUPERADMIN and ASSISTANT in referencesClients (93b6f45)
- feat: enhance access control for ROLE_TECHNICO_COMMERCIAL in AuthGuardService (e66bcc7)
- feat: update routing to include ASSISTANT role in authorities for referencesClients (9270b2c)
- feat: enforce role-based access for execution-batch and gestion-utilisateur components (4983dc3)
- feat: enhance filter functionality and restrict data retrieval for non-SUPERADMIN roles (0da40ef)
- feat: update routing and component logic to enforce role-based access for SUPERADMIN and ASSISTANT (fbd23d7)
- feat: update authorities for Winpharm Clients and InfoTechniqueComponent to include TECHNICO_COMMERCIAL and ASSISTANT roles (7437304)
- feat: add sorting functionality to DiffPrixWinplus component and update service methods (ff2925d)
- feat: add nomPharmacien and raisonSociale fields to InfoTechnique and update filter form (2e6db42)
- chore: release 1.0.49 by (Hicham Dadda) (113838d)

## v1.0.49
**Released:** 2025-07-09 16:36:04 +0100

- feat: add gsmResponsable field to TechniqueInfo and update related forms (13070bd)
- feat: add data attributes for InfoTechnique route to enforce authority checks (f5233d0)
- feat: refactor AuthGuardService for improved authentication and authority checks (94c3031)
- feat: update InfoTechnique component to enhance Winpharm version handling and improve grid functionality (b87b2e0)
- feat: enhance Différence Prix Winplus component with pagination and update functionality (b914de1)
- feat: implement Différence Prix Winplus component with routing, service integration, and grid display (5c962b8)
- feat: add Différence Prix Winplus component with routing and service integration (bede612)
- feat: update user data handling in edit mode to set password to null instead of deleting it (f715eea)
- feat: update Actions column in user management grid with action icons and adjust width (c3eb534)
- feat: enhance Info Technique component with filter and edit functionality, including form handling and modal integration (1d77b38)
- feat: add pagination functionality to Info Technique component with page change handling (4d47daf)
- feat: implement user search functionality with filter options and update user management API endpoints (960eab9)
- feat: add Info Technique feature with service, model, component, and routing (7b542ee)
- feat: implement user management with create/edit functionality and pagination (fe746be)
- fix: adjust styling for Raison Sociale column in anomalies grid (d403d83)
- chore: release 1.0.48 by (Hicham Dadda) (6b0ef26)

## v1.0.48
**Released:** 2025-07-07 10:52:54 +0100

- fix: filter out specific sites in loadAllGrossistes method for (produit envoie) (f9ab975)
- feat :  add edit client groupe depuis cliennt sites liee (04b7a03)
- chore: release 1.0.47 by (Hicham Dadda) (6de41f2)

## v1.0.47
**Released:** 2025-07-04 12:07:05 +0100

- fix: enhance produitSiteCriteria assignment logic in loadProduitsSite method (b5a2a15)
- chore: release 1.0.46 by (Hicham Dadda) (7938ee3)

## v1.0.46
**Released:** 2025-07-02 17:22:08 +0100

- fix: update loading logic for product stats display in dashboard (4a32837)
- fix: update navigation logic for product supplier exploration based on target type (5ac744f)
- feat: add 'estActif' field to filter form for winplus criteria (51896bd)
- chore: release 1.0.45 by (Hicham Dadda) (d68a169)

## v1.0.45
**Released:** 2025-06-30 16:27:23 +0100

- fix: set default value for statut in anomalieFilterForm to 'ouverte' (54cd5d0)
- fix: update statut type in IAnomalieCriteria and AnomalieCriteria to allow specific string values (d004eac)
- fix: reorder initialization in ngOnInit to ensure filter form is set up before fetching available sites (11750d5)
- feat: add 'estActif' field to ProduitSiteCriteriaForm and update form handling in list-produits-sites component (2f92b66)
- fix: correct mapping of site codes in criteria assignment (a15eac7)
- chore: release 1.0.44 by (Hicham Dadda) (6542a0f)

## v1.0.44
**Released:** 2025-06-30 14:53:23 +0100

- feat: integrate custom select component for site selection and clear state on filter reset (36499d2)
- feat: enhance custom select component with focus handling and improved click outside detection (f859044)
- chore: release 1.0.43 by (Hicham Dadda) (567f514)

## v1.0.43
**Released:** 2025-06-30 14:01:31 +0100

- feat: add clear button and placeholder to custom select for site selection (62dfd8e)
- feat: enhance click outside handling for dropdown with manual listener (ea4e67a)
- feat: integrate ProduitService and custom select component for site selection in produits list (9ccbe10)
- fix: update tag overflow text and improve placeholder styling; add thin scrollbar for options list (5730d3d)
- feat: implement custom select component with multi-select and search functionality (268af09)
- feat: add conditional rendering for duplicate anomalies in the client group list (7ca48c2)
- chore: release 1.0.42 by (Hicham Dadda) (f46c552)

## v1.0.42
**Released:** 2025-06-26 10:35:08 +0100

- feat: replace fixed consultation drawer with custom drawer component for improved UI (9f3d1a0)
- chore: release 1.0.41 by (Hicham Dadda) (78462f7)

## v1.0.41
**Released:** 2025-06-25 17:52:38 +0100

- feat: add indexOfGood property to Anomalie and ClientGroupe models; add corrige button  and functionalities (4e5e0bb)
- chore: release 1.0.40 by (Hicham Dadda) (9f9a8e9)

## v1.0.40
**Released:** 2025-06-25 13:47:33 +0100

- feat: enhance localité handling in client groupe and client site forms (74329f9)
- feat: refactor client groupe loading logic and reset filter form on clear (5204ec7)
- feat: enable editable input for localité in client site and client groupe filters (36c3bb1)
- chore: release 1.0.39 by (Hicham Dadda) (68752bc)

## v1.0.39
**Released:** 2025-06-25 12:08:59 +0100

- feat: add loading indicator for linked client sites and reset active tab on initialization (f9c3336)
- feat: add 'Envoyé' filter and date column to product lists (groupe & envoie) + (4c301b4)
- feat: call getPlatformUsage in ngOnInit for improved platform data handling (30e7b91)
- chore: release 1.0.38 by (Hicham Dadda) (074b864)

## v1.0.38
**Released:** 2025-06-25 11:03:11 +0100

- fix: ensure footer content is always rendered in custom drawer (7f951bc)
- feat: add active status styling to client site codes in grid (819d14d)
- chore: release 1.0.37 by (Hicham Dadda) (b2cba33)

## v1.0.37
**Released:** 2025-06-24 15:55:33 +0100

- feat: enhance site code display with active status styling (a5ea456)
- feat: add visual indicators for active and excluded client sites (8c9d623)
- chore: release 1.0.36 by (Hicham Dadda) (beb45ad)

## v1.0.36
**Released:** 2025-06-24 12:26:26 +0100

- feat: enhance client group creation with success and error alerts (4445da1)
- fix: correct class name in kendo-grid for consistent styling (376922c)
- refactor: remove default category selection in filter form on category load (0b46e7d)
- chore: release 1.0.35 by (Hicham Dadda) (1c24cc9)

## v1.0.35
**Released:** 2025-06-24 10:57:14 +0100

- feat: add client group exclusion functionality and update codeClientGroupe type to string (2704ba4)
- refactor: change codeClientGroupe type from number to string across models and components (7dfeb50)
- chore: release 1.0.34 by (Hicham Dadda) (a6bb0ee)

## v1.0.34
**Released:** 2025-06-23 15:32:51 +0100

- feat: implement advanced filtering options in product list envoie (d0e5de8)
- fix: remove redundant PFHT column from product grid (0b376e0)
- feat: add product summaries modal to display already sent products (e4dfe09)
- feat: add tracability check before sending products and enhance product list display (f82312a)
- chore: release 1.0.33 by (Hicham Dadda) (90a62c0)

## v1.0.33
**Released:** 2025-06-20 12:07:51 +0100

- fix : new fixes........ (b5b7509)
- chore: release 1.0.32 by (Hicham Dadda) (a21d254)

## v1.0.32
**Released:** 2025-06-18 12:29:55 +0100

- fix :  categorie not changed after load (4c4552d)
- chore: release 1.0.31 by (Hicham Dadda) (75233f6)

## v1.0.31
**Released:** 2025-06-17 15:06:04 +0100

- fix: update confirmation button text for detaching client site (9dc5aca)
- feat: enhance platform usage logic to display relevant platforms for client group (c1c643a)
- feat: update AI suggestions section to improve user experience and localization (406eb28)
- chore: release 1.0.30 by (Hicham Dadda) (c585129)

## v1.0.30
**Released:** 2025-06-16 15:44:19 +0100

- feat: set default category to 'Medicaments' in produit site criteria and filter form (ad45d8d)
- feat: adjust alert grid height to a fixed value and improve layout responsiveness (65f4b5c)
- feat: update actions column with button for detaching client site (4cebadd)
- feat: add alert expansion toggle functionality and enhance UI controls (445bc46)
- chore: release 1.0.29 by (Hicham Dadda) (04a3e74)

## v1.0.29
**Released:** 2025-06-16 13:50:43 +0100

- feat: add action column for alert management with status change functionality (9472840)
- feat: enhance alert management with tab navigation and filtering options +  add alert produit (d6497ca)
- feat: add alert management functionality to dashboard component (58e9ace)
- feat: add AlertProduit and CriteriaAlertProduit models for alert management (6e60a37)
- feat: update ClientGroupeLinkedSitesComponent to improve column titles and enhance navigation styles (6ef0220)
- feat: update topbar component to remove unnecessary container class (to fix deconection dropDown not showing overflow-hidden) (e5b089a)
- feat: remove error handling and debug logs from platform and client site retrieval methods (3b2dd0d)
- feat: enhance ClientGroupeLinkedSitesComponent with platform usage retrieval and improve tab navigation (60d40d8)
- feat: enhance ClientGroupeLinkedSitesComponent with platform usage retrieval and clean up imports (8c349d6)
- feat: update platform service and model for improved type safety and consistency (a216560)
- feat: add AI suggestions and manual search for product designation to change product groupe dsg (b4f5e2e)
- feat: add modal for editing product designation with validation (fdecde3)
- feat: enhance accessibility and usability of custom drawer component (96fa764)
- feat: add WinClientPlatformService and platform model definitions (b67db30)
- feat: add input fields for minimum and maximum matching percentage in anomalies list (d8a0156)
- feat: replace filter drawer with custom component for improved structure and usability (e7c32e1)
- fix: prevent consulting anomalies on empty column field click (3a9b1af)
- feat: replace transcodage drawer with custom component for improved structure and styling (606de50)
- chore: release 1.0.28 by (Hicham Dadda) (4d93c62)

## v1.0.28
**Released:** 2025-06-12 10:23:19 +0100

- feat: refactor filter modal for Produit Winplus with improved structure and styling (4d5e0ce)
- feat: add sorting functionality to winplus products grid and update service to support sorting parameters (17f2494)
- feat: add sorting functionality to linked product sites grid (289e806)
- feat: update anomaly date format and improve client group label for clarity (354f4c9)
- feat: enhance client group search functionality with pagination and sorting options (4cf1bc8)
- feat: add sorting functionality to linked client sites grid and enhance cell templates (3a460bd)
- feat: enhance execution batch page layout and styling for improved user experience (6859cbd)
- feat: enhance anomaly criteria and add new fields for filtering in the anomalies list (807cc47)
- feat: implement tooltip directive and component with customizable options (a794f6e)
- chore: release 1.0.27 by (Hicham Dadda) (12d5a12)

## v1.0.27
**Released:** 2025-06-11 12:11:05 +0100

- feat: update consultation drawer visibility logic to include client group conditions (2460c95)
- feat: enhance anomaly criteria with classification and duplicate client group code fields (87a5bfa)
- feat: add sorting functionality to anomalies grid and update service for search criteria (ddda61d)
- feat: add modal for editing long product  groupe designations  (pass 30 chars) and validation logic (48891ce)
- feat: add row class for long product designations and highlight in grid (ddef5e2)
- feat: implement cell click handling and enhance client group consultation logic (31f6a6f)
- feat: make client group title dynamic by using input property (57d3c23)
- chore: release 1.0.26 by (Hicham Dadda) (1b30231)

## v1.0.26
**Released:** 2025-06-10 15:33:56 +0100

- refactor: update filter and create client group drawers for improved structure and usability (17ffe1e)
- fix: handle optional chaining in buildProduitSiteFromWinplus for safer property access (39c5acd)
- feat: add sortable headers to Kendo grid columns for improved data interaction (fd586cb)
- fix: enhance scrollbar styling in CustomDrawerComponent body for better usability (2a4029a)
- feat: add action column to ClientGroupeLinkedSites for detaching client sites with confirmation dialog (9a7452c)
- fix: update loader component to display correctly based on showCount and adjust preloader position and z-index (8f932b4)
- refactor: streamline pagination handling in ClientSiteService and related components (d8ee21a)
- feat: add shake effect to CustomDrawerComponent for enhanced user interaction (3a65dc7)
- feat: add CustomDrawerComponent with customizable title, body, and footer (479afa1)
- fix: update ClientSitePipe transform method to return site name only (245cbda)
- feat: add ClientGroupeLinkedSites component with linked client sites functionality (59c878f)
- chore: release 1.0.25 by (Hicham Dadda) (729bc48)

## v1.0.25
**Released:** 2025-06-09 15:58:00 +0100

- feat: add codeSite column to client group and client site grids with clientSitePipe transformation (9f910d6)
- feat: add ClientSitePipe for site transformation and initialize services in AppComponent (e290e21)
- feat: enhance anomaly comparison with highlighted differences and similarity checks (10d8880)
- feat: add initialization methods for WinclientVilleService and WinclientSitesService (2fd8ed4)
- feat: add classification field to IClientGroupe and update list anomalies component with doublon differences summary (f745e5c)
- feat: enhance anomaly grid with clickable client group code and conditional percentage badge (57545be)
- feat: add validation functions for single character words and short names in anomaly consultation (77d22e8)
- feat: enhance anomaly consultation with specific anomaly types and highlight functionality (300c325)
- feat: update anomaly model to use AnomalieStringEnum and enhance client group consultation functionality (5c2d8d4)
- feat: add 'Champ manquant' anomaly type and update filtering options in List Anomalies Client Groupe (d402e31)
- feat: add filter drawer and enhance filtering options for anomalies (a3a6d9e)
- feat: implement search functionality for anomalies and enhance filtering criteria (c2d72e6)
- feat: refactor product group creation logic and extract buildProduitSiteFromWinplus method (def236a)
- feat: enhance List Anomalies Client Groupe with row highlighting and clear clicked item functionality (e5c8662)
- chore: release 1.0.24 by (Hicham Dadda) (bd4121e)

## v1.0.24
**Released:** 2025-06-05 16:53:10 +0100

- feat: add copy functionality for Code Client Groupe in List Anomalies Client Groupe (30098f1)
- feat: enhance List Anomalies Client Groupe with clickable anomaly links and query parameter handling (580fed8)
- refactor: remove default values from ProduitWinplus class properties (7ee2e9b)
- feat: add List Anomalies Client Groupe component and related services (a3fac0e)
- feat: add ProduitWinplus model and update references in list-produit-winplus component (aeb9867)
- refactor: remove unused ProduitSiteDto model and update references to use ProduitSite (ec51933)
- fix: adjust container class for execution batch page layout (2b08adc)
- feat: add platform configuration module with execution batch and user management components (b5670bf)
- chore: release 1.0.23 by (Hicham Dadda) (e2bc114)

## v1.0.23
**Released:** 2025-06-04 17:51:55 +0100

- feat: enhance client site and group search with pagination and sorting capabilities (8a65062)
- feat: add sortable column for Code sophatel in kendo-grid (566cb79)
- feat: Add ListProduitsSitesComponent with routing and styling (a7c0311)
- feat: implement sortable grid headers and update styles for better visibility (a5611cc)
- feat: adjust dashboard layout and add anomalies client group card (7ac070d)
- feat: add GridSortHeaderComponent for sortable grid headers (9522439)
- feat: add text-end class to pricing columns in kendo-grid for better alignment (5933bad)
- feat: update kendo-grid binding and enable sorting for linked product sites (6078cd2)
- feat: enhance transcodage modal with dynamic title and add copy functionality for multiple fields (1e2ff74)
- feat: add date creation fields to ProduitSiteCriteriaForm and update template bindings (625d2a8)
- chore: release 1.0.22 by (Hicham Dadda) (c04275c)

## v1.0.22
**Released:** 2025-05-30 11:19:03 +0100

- feat: add copy cell functionality for idSource and codeBarre columns in list-produits (sites) component (e9d54a1)
- chore: release 1.0.21 by (Hicham Dadda) (aab4cbb)

## v1.0.21
**Released:** 2025-05-29 17:11:31 +0100

- feat: add association drawer for linked client sites (client groupe) and implement loading states (3e107fe)
- feat: implement copy cell functionality for client site columns and update loading states (3d1fa9d)
- fix: add conditional rendering for copy cell component to prevent empty display (e759edb)
- feat: add copy functionality to grid columns for codeGroupe, codeWinplus, and idSource (a89ff05)
- fix: update icons for product and client references in menu configuration (1fe2d9a)
- feat: update grid pager styles and add hover effect for copy icon (8e44953)
- fix: reduce margin for form fields and update button styles for better UI consistency (produit site) (0530726)
- feat: enhance product search functionality with advanced search option and input trimming (winplus) (f4ade42)
- feat: enhance CopyCell component with animation and extend clipboard copy duration (05f5f6b)
- feat: add CopyCell component and CopyToClipboard directive for clipboard functionality (7bcb5c7)
- feat: add search functionality to filter provinces, sites, and villes; update action icon styles (90563e1)
- fix: improve drawer performanance in client site (cf4d10f)
- fix: change position style from absolute to fixed for drawer components (901b3f8)
- fix: change position style from absolute to fixed for drawer and update related styles (de26086)
- feat: add product  linked product site  search with loading state and retrieval functionality (a060507)
- fix: change overflow property to hide horizontal overflow in content and container-fluid (acb22fd)
- feat: add segment property to IClientGroupe and IClientGroupeCriteria, and implement segment selection in list-client-groupe component (50f12ab)
- chore: release 1.0.20 by (Hicham Dadda) (71f2fe7)

## v1.0.20
**Released:** 2025-05-27 10:07:24 +0100

- feat: update ProduitService to return Noeud[] in getAllGrossiste and refactor related logic in ListProduitsEnvoieComponent (1268bf3)
- feat: add dynamic select/deselect functionality for site selection with clear button (4176829)
- feat: enhance product processing (declare for trait) confirmation with dynamic messaging (126bba4)
- fix: update button labels for product transcoding to improve clarity (6b6f104)
- chore: release 1.0.19 by (Hicham Dadda) (a1fce48)

## v1.0.19
**Released:** 2025-05-26 14:16:23 +0100

- feat: add loading state for product group search functionality (213bd4f)
- feat: add overflow hidden style to content class (to fix content shift when open  transco sheet) (e192c9b)
- feat: add event service for sidebar management and update input placeholders in product list (7809617)
- chore: release 1.0.18 by (Hicham Dadda) (70cbe4d)

## v1.0.18
**Released:** 2025-05-26 12:59:09 +0100

- feat: update product forms and add loading indicators for Winplus search (e3a2139)
- change folder name from produit winplus to prduit envoie (4a98e59)
- feat: enhance product filter form with additional fields and validation (9a911c7)
- fix  : fix product groupe filters (88e2679)
- chore: release 1.0.17 by (Hicham Dadda) (c90e290)

## v1.0.17
**Released:** 2025-05-23 14:59:14 +0100

- fix : fix site label not shown (cc42181)
- chore: release 1.0.16 by (Hicham Dadda) (39b7cb6)

## v1.0.16
**Released:** 2025-05-23 12:53:30 +0100

- feat : add button maj produit site in dashboard (c0f1a22)
- fix : make inpe null by df (fb01b68)
- chore: release 1.0.15 by (Hicham Dadda) (18aa4f1)

## v1.0.15
**Released:** 2025-05-23 09:50:25 +0100

- fix :  inpe miss validated by another validator (4648c0b)
- chore: release 1.0.14 by (Hicham Dadda) (e81339e)

## v1.0.14
**Released:** 2025-05-22 16:46:55 +0100

- feat : add search criteria winplus  + tap + fix labels spellings (46a8ff6)
- feat :  add functionalries to detacher and atacher produit site and groupe (992c56e)
- fix :  modal text now is bigger (Confri,m Modal) (f580eba)
- fix : migrate produit fornisseur to diff page with produit site and groupe (c9d9efe)
- fix : add new pages for  winplus + site +groupe products (a648498)
- fix : latest changes of produit fournisseur (b8e98dc)
- feat : add new product fournisseur component and add products stats to dash (bda52b0)
- fix :  sort ville and add filter by libelle (f4f97bc)
- chore: release 1.0.13 by (Hicham Dadda) (a181cd6)

## v1.0.13
**Released:** 2025-05-06 13:29:01 +0000

- feat  : improve NouveauxClient Card visual hierarchy (be87f26)
- chore: release 1.0.12 by (Hicham Dadda) (f51ecf0)

## v1.0.12
**Released:** 2025-05-06 10:08:10 +0000

- fix :  make client site drawers responsive (63893f8)
- feat :  add filter by libelle to localite (979a295)
- feat : add pagination to sites list (0526401)
- fix :  fix client site date got sent in filter even it undefined (8742d81)
- fix : dev env file has wrong base url of winclient (b029e55)
- fix :  font-weight for nvClient intervals (f164db4)
- feat : add update localite (f736738)
- fix : typo in dashboard (f701209)
- chore: release 1.0.11 by (Hicham Dadda) (8e6e719)

## v1.0.11
**Released:** 2025-05-05 13:03:07 +0000

- fix :  remove unmeanful label from newCleintCoont Cards (47eb0ab)
- feat :  add nouveaux client filter per interval (600d396)
- fix : lsit client site disbale dates on filter if nouveauClient is not selected (5b8b169)
- chore: release 1.0.10 by (Hicham Dadda) (3336461)

## v1.0.10
**Released:** 2025-05-05 10:25:47 +0000

- fix :  envoie produit boolean fix ajouter ou modiff (237fd2d)
- feat :  add province + localile list and  pages (6c4b90b)
- chore: release 1.0.9 by (oumaima) (a1fdd70)

## v1.0.9
**Released:** 2025-05-02 16:47:01 +0100

- feat: add all sites button + update sucess message (c5815bb)
- chore: release 1.0.8 by (Hicham Dadda) (04ccc7e)

## v1.0.8
**Released:** 2025-05-02 09:48:38 +0000

- fix : assisstant AI Refresh After Modal Re-open (19dc882)
- fix : date Maj Color not working right (057689f)
- fix posible error in list site (e3b998a)
- chore: release 1.0.7 by (oumaima) (5794b33)

## v1.0.7
**Released:** 2025-04-30 14:37:28 +0100

- feat: update app logo and title fix: Apply the corrected nextPage function (1471c64)
- chore: release 1.0.6 by (Hicham Dadda) (cffa336)

## v1.0.6
**Released:** 2025-04-30 11:51:05 +0000

- change dash layout +  add alert clinent modiffs (c59e1c6)
- fix :  change prod base_url (d830792)
- map api of winclient to new /winclient endpoint (aa36f1f)
- chore: release 1.0.5 by (oumaima) (5032a43)

## v1.0.5
**Released:** 2025-04-10 16:24:28 +0100

- feat: add success message (f11e822)
- chore: release 1.0.4 by (oumaima) (c38910d)

## v1.0.4
**Released:** 2025-04-08 14:17:40 +0100

- chore: release 1.0.3 by (oumaima) (0007656)

## v1.0.3
**Released:** 2025-04-08 14:12:51 +0100

- update backend link (ab7e066)
- chore: release 1.0.2 by (oumaima) (75a5df5)

## v1.0.2
**Released:** 2025-04-08 12:41:34 +0000

- feat :  add Actions sticky colum (ae35019)
- feat :  add filter drawer for client groupe +  labels text fix (a91d419)
- feat :  inhance toast apperance and style (a309095)
- fix :   env Build error (base url not exist in all ens) (29c7f6d)
- fixes :  move dashboard to main dash + finish client site + groupe functionalities (51ec234)
- chore: release 1.0.1 by (oumaima) (50e2add)

## v1.0.1
**Released:** 2025-03-28 11:41:09 +0000

- feat: add new filtering fields + add confirmation modal (fbd29dc)
- fix : move dashboard clinet ref to main dashboard (f7d3221)
- fix : fix issue with selection grid (ckeckbox not updated) (bcad1d4)
- deat: update grid fields + filters (d7468f8)
- feat : add map select for create Client Groupe and add association Drawer + Assistant AI (0e8cd42)
- feat: consume noeud api to display sites list (efe7f46)
- latest Modiffs before big changes (Associations SiteBar) (2f0fbad)
- style: update components style (d3f09d0)
- feat: add envoie modal and grid (ab1a500)
- deat: update winplus produits grid (c85a6e7)
- fix : remove delete client groupe (7e5c8c3)
- fix : complete Client Groupe Crud (ce13f2e)
- feat: add liste produits winplus component and routing (b044c2c)
- feat :  add grid styling (0b64471)
- feat :  add config for nexus publisher (f51336f)
- feat:update api naming (ef5bde1)
- feat :  inhance modal appearence (bd8060b)
- feat :  add base client ref component (moved from winpharm) (3d1590d)
- style : update grid and footer (7020c0d)
- clean and init client ref (40b1b8b)
- feat: add pager + update filter (d7b7866)
- fix: remove login tenant (bc2fa10)
- feat: update login + list produits (649bb1d)
- feat: add products list init (726e8a8)
- feat: update routing (f02d81b)
- style: update theme and logos (be2c00b)

