import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { Anomalie, AnomalieCriteria } from '../models/anomalie.model';
import { Page } from '../../referential/models/Page/page.model';
import { Pagination } from '../../referential/models/pagination.interface';

@Injectable({
  providedIn: 'root'
})
export class WinclientAnomaliesService {

constructor(
  private http: HttpClient
) { }


  getAllAnomalies(pagination:Pagination) {
    return this.http.get<Page<Anomalie>>(`${env.winclient_base_url}/api/winclient/anomalies`, {
      params: {
        page : String(Math.floor(pagination.skip / pagination.pageSize) ?? 0),
        size : String(pagination.pageSize ?? 21)
      }
    });
  }

  searchAnomalies(pagination:Pagination, criteria:Partial<AnomalieCriteria>) {
    return this.http.post<Page<Anomalie>>(`${env.winclient_base_url}/api/winclient/anomalies/search`, criteria,{
      params: {
        page : String(Math.floor(pagination.skip / pagination.pageSize) ?? 0),
        size : String(pagination.pageSize ?? 21),
        sort: pagination.sortField ? `${pagination.sortField},${pagination.sortMethod}` : ''
      }
    });
  }

  changerStatutAnomalie(id: number, nouveauStatut: string) {
    return this.http.put(`${env.winclient_base_url}/api/winclient/anomalies/${id}/statut`, {}, {
      params: { nouveauStatut }
    });
  }
  
  getAnomaliesByStatut(statut: string, pagination:Pagination = {}) {
    return this.http.get<Page<Anomalie>>(`${env.winclient_base_url}/api/winclient/anomalies/statut/${statut}`, {
      params: {
        page : String(Math.floor(pagination.skip / pagination.pageSize) ?? 0),
        size : String(pagination.pageSize ?? 21)
      }
    });
  }

  lancerBatch() {
    return this.http.post(`${env.winclient_base_url}/api/winclient/anomalies/update-anomlies`, {});
  }

}
