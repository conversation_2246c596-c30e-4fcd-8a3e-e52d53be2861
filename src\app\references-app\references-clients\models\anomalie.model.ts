import { ClientGroupe } from "./clientGroupe";

interface IAnomalie {
  anomalie: AnomalieStringEnum;
  clientGroupeId: number;
  codeClientGroupe: string;
  codeClientGroupeDoublon: string;
  dateCreationAnomalie: string;
  dateTraitement?: string;
  pourcentageMatching?: number;
  id: number;
  statut?: StatutAnomalie;
    indexOfGood: number;

}

export enum AnomalieType {
  DOUBLON_EXACT = 'DOUBLON_EXACT',
  DOUBLON_PROBABLE = 'DOUBLON_PROBABLE',
  RAISON_VILLE = 'RAISON_VILLE',
  NOM_CAR_1 = 'NOM_CAR_1',
  NOM_TROP_COURT = 'NOM_TROP_COURT',
  CHAMP_MANQUANT = 'CHAMP_MANQUANT'
}

export enum AnomalieStringEnum {
  DOUBLON_EXACT = 'Doublon exact',
  DOUBLON_PROBABLE = 'Doublon probable',
  RAISON_VILLE = 'Raison sociale contient une ville',
  NOM_CAR_1 = 'Un mot de 1 caractère',
  NOM_TROP_COURT = 'Nom trop court',
  CHAMP_MANQUANT = 'Champ manquant'
}

interface IAnomalieCriteria {
  anomalieType: AnomalieType;
  codeClientGroupe: string;
  dateCreationAnomalie: string;
  classification: string;
  codeClientGroupeDoublon: string;
  dateTraitement: string;
  statut: "ouverte" | "fermee" | null;
  nomPharmacien: string;
  pourcentageMatchingMax: number;
  pourcentageMatchingMin: number;
  raisonSociale: string;
  villeId: number;
  villeLibelle: string;
  estUtilise: boolean;
}


export class AnomalieCriteria implements IAnomalieCriteria {
  anomalieType: AnomalieType;
  codeClientGroupe: string;
  dateCreationAnomalie: string;
  classification: string;
  codeClientGroupeDoublon: string;
  dateTraitement: string;
  statut: "ouverte" | "fermee" | null;
  nomPharmacien: string;
  pourcentageMatchingMax: number;
  pourcentageMatchingMin: number;
  raisonSociale: string;
  villeId: number;
  villeLibelle: string;
  estUtilise: boolean;
  
  constructor(anomalieCriteria: Partial<IAnomalieCriteria>) {
    Object.assign(this, anomalieCriteria);
  }
}


export class Anomalie implements IAnomalie {
  anomalie: AnomalieStringEnum;
  clientGroupeId: number;
  codeClientGroupe: string;
  clientGroupe: ClientGroupe;
  dateCreationAnomalie: string;
  dateTraitement: string;
  pourcentageMatching: number;
  codeClientGroupeDoublon: string;
  id: number;
  statut: StatutAnomalie;
  indexOfGood: number;

  constructor(anomalie: Partial<IAnomalie>) {
    Object.assign(this, anomalie);
  }
}



export enum StatutAnomalie {
  FREMEE="fermee",
  OUVERTE="ouverte"
}