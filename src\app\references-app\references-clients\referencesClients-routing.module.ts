import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { LayoutContainerComponent } from "src/app/layouts/layout-container.component";
import { AuthGuardService } from "src/app/shared/services/auth-guard.service";
import { ListClientGroupeComponent } from "./pages/client-groupe/list-client-groupe/list-client-groupe.component";
import { ListVillesComponent } from "./pages/ville/list-villes/list-villes.component";
import { ListSitesComponent } from "./pages/site/list-sites/list-sites.component";
import { ListRegionComponent } from "./pages/region/list-region/list-region.component";
import { ListClientSitesComponent } from "./pages/client-site/list-client-sites/list-client-sites.component";
import { WinclientAcceuilComponent } from "./pages/acceuil/winclient-acceuil.component";
import { ProvinceComponent } from "./pages/province/province.component";
import { LocaliteComponent } from "./pages/localite/localite.component";
import { ListAnomaliesClientGroupeComponent } from "./pages/anomalies/list-anomalies-client-groupe/list-anomalies-client-groupe.component";
import { InfoTechniqueComponent } from "./pages/info-technique/info-technique.component";

const routes: Routes = [

  {
    path: "client-groupe",
    component: LayoutContainerComponent,
    children: [
      {
        path: '',
        component: ListClientGroupeComponent,
      }
    ],
    canActivate: [AuthGuardService],
    data: {
      authorities: ["ROLE_SUPERADMIN","ROLE_ASSISTANT"]
    },
  },
  {

    path: "info-technique",
    component: LayoutContainerComponent,
    data: {
      idComp: "InfoTechniqueComponent",
      authorities: ["ROLE_SUPERADMIN", "ROLE_TECHNICO_COMMERCIAL", "ROLE_ASSISTANT"]
    },
    children: [
      {
        path: '',
        component: InfoTechniqueComponent,
      }
    ],
    canActivate: [AuthGuardService],
  },
  {
    path: "anomalies-client-groupe",
    component: LayoutContainerComponent,
    children: [
      {
        path: '',
        component: ListAnomaliesClientGroupeComponent,
      }
    ],
    canActivate: [AuthGuardService],
    data: {
      authorities: ["ROLE_SUPERADMIN","ROLE_ASSISTANT"]
    },
  },
  {
    path: "villes",
    component: LayoutContainerComponent,
    children: [
      {
        path: '',
        component: ListVillesComponent,
      }
    ],
    canActivate: [AuthGuardService],
    data: {
      authorities: ["ROLE_SUPERADMIN","ROLE_ASSISTANT"]
    },
  },
  {
    path: "client-sites",
    component: LayoutContainerComponent,
    children: [
      {
        path: '',
        component: ListClientSitesComponent,
      }
    ],
    canActivate: [AuthGuardService],
    data: {
      authorities: ["ROLE_SUPERADMIN","ROLE_ASSISTANT"]
    },
  },
  {
    path: "regions",
    component: LayoutContainerComponent,
    children: [
      {
        path: '',
        component: ListRegionComponent,
      }
    ],
    canActivate: [AuthGuardService],
    data: {
      authorities: ["ROLE_SUPERADMIN","ROLE_ASSISTANT"]
    },
  },
  {
    path: "sites",
    component: LayoutContainerComponent,
    children: [
      {
        path: '',
        component: ListSitesComponent,
      }
    ],
    canActivate: [AuthGuardService],
    data: {
      authorities: ["ROLE_SUPERADMIN","ROLE_ASSISTANT"]
    },
  },
  {
    path: "provinces",
    component: LayoutContainerComponent,
    children: [
      {
        path: '',
        component: ProvinceComponent,
      }
    ],
    canActivate: [AuthGuardService],
    data: {
      authorities: ["ROLE_SUPERADMIN","ROLE_ASSISTANT"]
    },
  },
  {
    path: "localites",
    component: LayoutContainerComponent,
    children: [
      {
        path: '',
        component: LocaliteComponent,
      }
    ],
    canActivate: [AuthGuardService],
    data: {
      authorities: ["ROLE_SUPERADMIN","ROLE_ASSISTANT"]
    },
  },

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ReferencesClientsRoutingModule { }
