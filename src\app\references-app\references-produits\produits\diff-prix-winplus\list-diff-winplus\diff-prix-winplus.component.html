<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">Synchronisation Produits Groupe-Winplus</h4>
      <div class="d-flex flex-wrap justify-content-end gap-2">
        <button class="btn btn-dark" (click)="openFilterDrawer()">
          <i class="mdi mdi-filter"></i>
          Filtrer
        </button>
      </div>
    </div>
  </div>
</div>


<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-body p-0">
        <kendo-grid class="content-wrap flex-shrink-0 ref-grid" [data]="diffPrixWinplusAndGroupe"
        style="height: calc(100vh - 130px);border-radius: 10px;" class="winClient-stats-grid clickable-grid ref-grid content-wrap custom-sort-grid"

          [pageable]="true"
          [pageSize]="navigation.pageSize"
          [skip]="navigation.skip"
          [sortable]="true"
          [sort]="sort"
          (sortChange)="sortChange($event)"
          (cellClick)="onCellClick($event)"
          [rowClass]="rowClass"
          >
            <kendo-grid-column field="codeWinplus" title="Code Winplus" [width]="100">
             <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeWinplus"></app-copy-cell>
          </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="codeGroupe" title="Code Groupe" [width]="100">
            <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeGroupe"></app-copy-cell>
          </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="designationWinplus" title="Designation Winplus" [width]="200">
             <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="designationGroupe" title="Designation Groupe" [width]="200">
             <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="prixVenteWinplus" title="PPV Winplus" class="text-end" [headerClass]="'text-start'" [width]="100"  [sortable]="false"></kendo-grid-column>
          <kendo-grid-column field="prixVenteGroupe" title="PPV Groupe" class="text-end" [headerClass]="'text-start'" [width]="100" [sortable]="false"></kendo-grid-column>
          <kendo-grid-column field="differencePrix" class="text-end" [headerClass]="'text-start'" title="Difference Prix" [width]="100">
             <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="prioriteVente" class="text-end" [headerClass]="'text-start'" title="Priorite Vente" [width]="100"  [sortable]="true">
            <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
             {{dataItem.prioriteVente | number: "1.2-2": "Fr-fr"}}
          </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="codeBarreWinplus" title="C.B Winplus" class="text-end" [headerClass]="'text-start'" [width]="100"  [sortable]="false"></kendo-grid-column>
          <kendo-grid-column field="codeBarreGroupe" title="C.B Groupe" class="text-end" [headerClass]="'text-start'" [width]="100"  [sortable]="false"></kendo-grid-column>
          <kendo-grid-column title="Action" [width]="100">
            <ng-template kendoGridCellTemplate let-dataItem>
              <app-action-icon [icon]="'magnify'"
              appTooltip="Consulter Produit Groupe"
              [extendClass]="'circle-lg'" (click)="openTranscoDrawer(dataItem)"></app-action-icon>
              <app-action-icon [icon]="'check-circle'"
              appTooltip="Mettre à jour le prix Winplus"
              [backgroundColor]="'success'"
              [extendClass]="'circle-lg'" (click)="updatePrixWinplus(dataItem)"></app-action-icon>
            </ng-template>
          </kendo-grid-column>
          <ng-template
          kendoPagerTemplate
          let-totalPages="totalPages" let-currentPage="currentPage" let-total="total">
            <wph-grid-custom-pager
              [totalElements]="total"
              [totalPages]="totalPages"
              [currentPage]="currentPage"
              [allowPageSizes]="false"
              [navigation]="navigation"
              style="width: 100%;"
              (pageChange)="pageChange($event)"></wph-grid-custom-pager>
        ></ng-template>
      </kendo-grid>
      </div>
    </div>
  </div>
</div>

<!-- Filter Drawer -->
<app-custom-drawer
  [isOpen]="isFilterDrawerOpen"
  [width]="'600px'"
  [title]="'Filtrer Différence Prix Winplus'"
  (isOpenChange)="isFilterDrawerOpen = $event">

  <div class="p-2" drawer-body>
    <form id="filterForm" [formGroup]="filterForm" (ngSubmit)="onFilterSubmit()" appFocusTrap>
      <div class="flex-grow-1">
        <div class="row">
          <div class="col-12 col-sm-6">
            <div class="mb-3">
              <label for="codeWinplus" class="form-label">Code Winplus</label>
              <input id="codeWinplus"  type="search" formControlName="codeWinplus" class="form-control"
                     placeholder="Tapez le code Winplus" appAutoFocus>
            </div>
          </div>
          <div class="col-12 col-sm-6">
            <div class="mb-3">
              <label for="codeGroupe"class="form-label">Code Groupe</label>
              <input id="codeGroupe"  type="search" formControlName="codeGroupe" class="form-control"
                     placeholder="Tapez le code groupe">
            </div>
          </div>
          <div class="col-12">
            <div class="mb-2">
              <label for="designationWinplus" class="form-label">Designation Winplus</label>
              <input id="designationWinplus"  type="search" formControlName="designationWinplus" class="form-control"
                     placeholder="Tapez la designation Winplus">
            </div>
          </div>
          <div class="col-12">
            <div class="mb-3">
              <label for="designationGroupe" class="form-label">Designation Groupe</label>
              <input id="designationGroupe"  type="search" formControlName="designationGroupe" class="form-control"
                     placeholder="Tapez la designation groupe">
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>

  <div drawer-footer class="modal-footer justify-content-start">
    <button type="submit" form="filterForm" class="btn btn-primary">
      Rechercher
    </button>
    <button type="button" class="btn btn-dark" (click)="resetFilter()">
      vider
    </button>
  </div>
</app-custom-drawer>


<!-- Transco Drawer -->
<app-custom-drawer
[isOpen]="showTranscoDrawer"
[width]="'70%'"
[title]="'Produit Groupe Transcodage'"
(isOpenChange)="closeTranscoDrawerAndReset()"
>
   <div class="modal-body p-1 pt-0 pb-0 d-flex flex-column" style="height: calc(100% - 46px); overflow: auto;">
    <div class="mt-1">
      <kendo-grid class="content-wrap flex-shrink-0 ref-grid" style="max-height: 200px;height:auto !important;" [kendoGridBinding]="produitGroupe" *ngIf="clickedItem?.codeGroupe && !isTranscoGroupeLoading">
        <ng-template kendoGridToolbarTemplate>
          <div [ngClass]="{'client-have-association-bg':false,'client-have-no-association-bg':true}" style="height: 44px;" class="d-flex justify-content-between align-items-center px-2">
            <span class="text-white fs-4 k-font-weight-bold">Produit Groupe</span>
          </div>
        </ng-template>
        <kendo-grid-column field="codeSite" title="Code Site" [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
        <span [ngClass]="{'active-vente-color font-weight-bold': dataItem?.actifVente}">
        {{dataItem.codeSite | sitePipe}}
      </span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="idSource" title="Code Local" [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.idSource"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="codeWinplus" title="Code Winplus" [width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeWinplus"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="codeGroupe" title="Code Groupe" [width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeGroupe"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="designation" title="Designation" [width]="200">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.designation"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixAchatStd" title="PPH" [width]="70" class="text-end">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{dataItem.prixAchatStd | number: "1.2-2": "Fr-fr"}}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixVenteStd" title="PPV" [width]="70" class="text-end">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{dataItem.prixVenteStd | number: "1.2-2": "Fr-fr"}}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="codeBarre" title="Code Barre"  class="text-end"  [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeBarre"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
      <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="isTranscoGroupeLoading">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-loading mdi-spin text-primary fs-1"></i>
        </div>
        <p class="font-weight-600 m-0">Chargement de produit groupe...</p>
       </div>
      <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="!clickedItem?.codeGroupe && !isTranscoGroupeLoading ">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-alert-circle-outline text-warning fs-1"></i>
        </div>
        <p class="font-weight-600 m-0">Ce Produit n'a aucun Produit Groupe Associé</p>
        <!-- refresh button -->
       </div>

    </div>
 <div class="mt-1 d-flex flex-column flex-grow-1">
      <kendo-grid class="content-wrap flex-shrink-0 ref-grid have-winplus-association-grid" style="max-height: 200px;height:auto !important;" [kendoGridBinding]="produitWinplus" *ngIf="clickedItem?.codeWinplus && clickedItem?.codeGroupe && !isTranscoWinplusLoading && produitWinplus?.length > 0">
        <ng-template kendoGridToolbarTemplate>
          <div [ngClass]="{'client-have-association-bg':false,'winplus-have-association-bg':true}" style="height: 44px;" class="d-flex justify-content-between align-items-center px-2">
            <span class="text-white fs-4 k-font-weight-bold">Produit Winplus</span>
          </div>
        </ng-template>
        <kendo-grid-column field="codeSite" title="" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="idSource" title="" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="codeWinplus" title="Code Winplus" [width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeWinplus"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="" title="" [width]="70">
          <!-- <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeGroupe"></app-copy-cell>
          </ng-template> -->
        </kendo-grid-column>
        <kendo-grid-column field="designation" title="Designation" [width]="200">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.designation"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixAchatStd" title="PPH" class="text-end" [width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{dataItem.prixAchatStd | number: "1.2-2": "Fr-fr"}}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixVenteStd" title="PPV" class="text-end" [width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            {{dataItem.prixVenteStd | number: "1.2-2": "Fr-fr"}}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="codeBarre" title="Code Barre"  class="text-end"  [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem.codeBarre"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
      <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="isTranscoWinplusLoading">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-loading mdi-spin text-primary fs-1"></i>
        </div>
        <p class="font-weight-600 m-0">Chargement de produit Winplus...</p>
       </div>
      <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="!clickedItem?.codeWinplus && clickedItem?.codeGroupe && !isTranscoWinplusLoading">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-alert-circle-outline text-warning fs-1"></i>
        </div>
        <p class="font-weight-600 m-0">Ce Produit n'a aucun Produit Winplus Associé</p>
         <button class="btn btn-dark mt-2 rounded-pill" *ngIf="clickedItem?.codeWinplus" (click)="getProduitWinplus()">Rafraîchir</button>
       </div>
        <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="isLinkedProductSiteLoading">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-loading mdi-spin text-primary fs-1"></i>
        </div>
        <p class="font-weight-600 m-0">Chargement des produit site...</p>
       </div>
        <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center"  *ngIf="!isLinkedProductSiteLoading && !isTranscoWinplusLoading && !isTranscoGroupeLoading && produitSiteLinkedToGroupe.data?.length === 0">
          <div class="d-flex justify-content-center align-items-center">
            <i class="mdi mdi-alert-circle-outline text-warning fs-1"></i>
          </div>
          <p class="font-weight-600 m-0">Ce Produit Groupe n'a aucun Produits site Lié</p>
          <button class="btn btn-dark btn-sm mt-2 rounded-pill" (click)="getProduitSiteLinkedToGroupe()">
            <i class="mdi mdi-refresh"></i>
            Rafraîchir</button>
       </div>
        <div class="flex-grow-1">
          <kendo-grid class=" content-wrap flex-shrink-0 client-have-association-grid ref-grid mt-1 custom-sort-grid"
          style="height: 100%;"
           [data]="produitSiteLinkedToGroupe"  [sortable]="{ mode: 'single' }"
           [sort]="productSiteLinkedToGroupeSort"
           [pageable]="true"
           [pageSize]="linkedProductSiteNavigation.pageSize"
           [skip]="linkedProductSiteNavigation.skip"
            (sortChange)="onSortChangeProductSiteLinkedToGroupe($event)"
           *ngIf="!isLinkedProductSiteLoading && !isTranscoWinplusLoading && !isTranscoGroupeLoading && produitSiteLinkedToGroupe.data?.length > 0">
          <ng-template kendoGridToolbarTemplate>
            <div [ngClass]="{'client-have-association-bg':true}" style="height: 44px;" class="d-flex justify-content-between align-items-center px-2">
              <span class="text-white fs-4 k-font-weight-bold">Produits site Liés</span>
              <button class="btn btn-dark btn-sm rounded-pill m-1" (click)="getProduitSiteLinkedToGroupe()" style="padding: 2px 6px;">
                <i class="mdi mdi-refresh"></i>
                Rafraîchir</button>
            </div>
          </ng-template>
          <kendo-grid-column field="codeSite" title="Code Site" [width]="100">
            <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  [type]="'numeric'"
              [active]="linkedProductSiteNavigation.sortField === column.field"
              [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
            <span [ngClass]="{'active-vente-color font-weight-bold': dataItem?.actifVente}">
              {{dataItem.codeSite | sitePipe}}
            </span>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="idSource" title="Code Local" [width]="100">
            <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  [type]="'numeric'"
              [active]="linkedProductSiteNavigation.sortField === column.field"
              [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
               <app-copy-cell [value]="dataItem.idSource"></app-copy-cell>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="codeWinplus" title="Code Winplus" [width]="70">
            <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
                <app-grid-sort-header [title]="column.title"   [type]="'numeric'"
                [active]="linkedProductSiteNavigation.sortField === column.field"
                [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
              <app-copy-cell [value]="dataItem.codeWinplus"></app-copy-cell>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="codeGroupe" title="Code Groupe" [width]="70">
             <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
                <app-grid-sort-header [title]="column.title"
                [active]="linkedProductSiteNavigation.sortField === column.field"
                [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
              <app-copy-cell [value]="dataItem.codeGroupe"></app-copy-cell>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="designation" title="Designation" [width]="200">
             <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
                <app-grid-sort-header [title]="column.title"
                [active]="linkedProductSiteNavigation.sortField === column.field"
                [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
              <app-copy-cell [value]="dataItem.designation"></app-copy-cell>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="prixAchatStd" class="text-end"  title="PPH" [width]="70">
             <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
                <app-grid-sort-header [title]="column.title"   [type]="'numeric'"
                [active]="linkedProductSiteNavigation.sortField === column.field"
                [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
              {{dataItem.prixAchatStd | number: "1.2-2": "Fr-fr"}}
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="prixVenteStd" class="text-end"  title="PPV" [width]="70">
             <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
                <app-grid-sort-header [title]="column.title"   [type]="'numeric'"
                [active]="linkedProductSiteNavigation.sortField === column.field"
                [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
            {{dataItem.prixVenteStd | number: "1.2-2": "Fr-fr"}}
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="codeBarre" title="Code Barre"  class="text-end"  [width]="100">
             <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
                <app-grid-sort-header [title]="column.title"
                [active]="linkedProductSiteNavigation.sortField === column.field"
                [direction]="linkedProductSiteNavigation.sortMethod"></app-grid-sort-header>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
              <app-copy-cell [value]="dataItem.codeBarre"></app-copy-cell>
            </ng-template>
          </kendo-grid-column>
          <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
          let-total="total">
          <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"  [allowPageSizes]="false"
            [navigation]="linkedProductSiteNavigation" style="width: 100%;"
            (pageChange)="linkedProductSitePageChange($event)"></wph-grid-custom-pager>
                </ng-template>
                </kendo-grid>
        </div>
    </div>
  </div>
</app-custom-drawer>