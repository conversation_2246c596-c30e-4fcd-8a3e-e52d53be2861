<app-custom-drawer [isOpen]="isFilterOpen" (isOpenChange)="isFilterOpen = $event"
[title]="'Filtrer les produits site'"
>
<form class="p-2" [formGroup]="filterForm" (ngSubmit)="filterSubmit()" id="filterForm"   appFocusTrap>
      <div class="flex-grow-1">

        <div class="row">
          <div class="col-md-6">
            <div class="mb-2">
              <label class="form-label">Designation</label>
              <input type="search" class="form-control" formControlName="designation" placeholder="tapez désignation">
            </div>
          </div>
         
          <div class="col-md-6">
                <div class="mb-2">
                  <label class="form-label">Code Sophatel</label>
                  <input type="search" class="form-control" formControlName="codeSophatel" placeholder="tapez code code Sophatel">
                </div>
                
          </div>
         
        </div>

        <div class="row">
  
          <div class="col-md-6">
            <div class="mb-2">
              <label class="form-label">Code Groupe</label>
              <input type="search" class="form-control" formControlName="codeGroupe" placeholder="tapez code groupe">
            </div>
          </div>
           <div class="col-md-6">
            <div class="mb-2">
              <label class="form-label">Code winplus</label>
              <input type="search" class="form-control" formControlName="codeWinplus" placeholder="tapez code winplus">
            </div>
          </div>
              <!--  -->
        </div>
        <div class="row">
  
          <div class="col-md-6">
            <div class="mb-2">
              <label class="form-label">Laboratoire</label>
              <input type="search" class="form-control" formControlName="labelleLabo" placeholder="tapez laboratoire">
            </div>
          </div>
           <div class="col-md-6">
            <div class="mb-2">
              <label class="form-label">Code Barre</label>
              <input type="search" class="form-control" formControlName="codeBarre" placeholder="tapez code barre">
            </div>
          </div>
              <!--  -->
        </div>

        
        <div class="row">
  
          <div class="col-md-6">
            <div class="mb-2">
              <label class="form-label">Date création Du</label>
              <app-date-picker formControlName="dateCreateDu"></app-date-picker>
            </div>
          </div>
           <div class="col-md-6">
            <div class="mb-2">
              <label class="form-label">Date création Au</label>
          <app-date-picker formControlName="dateCreateAu"></app-date-picker>
            </div>
          </div>
              <!--  -->
        </div>
        <div class="row flex-column  gap-2">
            <div class="col-12 d-flex flex-column flex-sm-row align-items-start align-items-sm-center mb-2 mb-sm-0">
                  <label for="envoyer" class="form-label me-2 mb-1 mb-sm-0 flex-shrink-0">Envoyé</label>
                  <div class="w-100">
                          <app-switch
                  [elements]='[{ label: "Tous", value:null},{ label: "Envoyé", value:true},{ label: "Non Envoyé", value:false}]'
                  formControlName="envoyer" name="envoyer" [disabled]="false"
                  [switchClass]="'info'"></app-switch>
            </div>
          </div>
          <div class="col-12 d-flex flex-column flex-sm-row align-items-start align-items-sm-center mb-2 mb-sm-0">
                <label for="groupementWinpluus" class="form-label me-2 mb-1 mb-sm-0 flex-shrink-0">Transco Winplus</label>
                <div class="w-100">
                        <app-switch
                [elements]='[{ label: "Tous", value:null},{ label: "Transcodé", value:true},{ label: "Non Transcodé", value:false}]'
                formControlName="transcoWinplus" name="groupementWinpluus" [disabled]="false"
                [switchClass]="'info'"></app-switch>
                </div>
          
          </div>
          <div class="col-12 d-flex flex-column flex-sm-row align-items-start align-items-sm-center mb-2 mb-sm-0">
                <label for="produitSupp" class="form-label me-2 mb-1 mb-sm-0 flex-shrink-0">Supprimé</label>
                <div class="w-100">
                        <app-switch
                [elements]='[{ label: "Tous", value:null},{ label: "Supprimé", value:true},{ label: "Non Supprimé", value:false}]'
                formControlName="produitSupp" name="produitSupp" [disabled]="false"
                [switchClass]="'info'"></app-switch>
                </div>
          
          </div>
        </div>
      </div>
   
    </form>
       <div class="modal-footer bg-white justify-content-start" drawer-footer>
        <div class="col-12 d-flex flex-wrap gap-2 justify-content-start">
          <button class="btn btn-primary" tabindex="-1" type="submit" form="filterForm">Recherche</button>
          <button class="btn btn-dark" tabindex="-1" type="button" (click)="clearFilter()"  >Vider</button>
        </div>
      </div>
</app-custom-drawer>



<div class="row mt-2">
    <!-- First Grid: Produits Winplus -->
    <div class="col-12" >
        <div class="page-title-box mb-1 row">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <h4 class="page-title ps-2">Liste produits Groupe</h4>
                <div class="d-flex flex-wrap justify-content-end me-2">
                  <button class="btn btn-dark me-2" (click)="openFilter()">
                    <i class="mdi mdi-filter pe-1"></i>
                    Filtrer</button>
                <button class="btn btn-primary" [disabled]="selectedItems.length === 0"  (click)="sendSelected()">Envoyer</button>

                </div>
            </div>
        </div>
        <kendo-grid
            class="border-grey me-2  ref-grid"
            style="height: 400px; cursor: pointer;"
            [data]="gridData"
            [pageable]="true"
            [pageSize]="navigation.pageSize"
            [skip]="navigation.skip"
        >
        <kendo-grid-column [width]="20" class="text-center">
                <ng-template kendoGridHeaderTemplate>
                    <input class="checkbox-grid" type="checkbox" (change)="selectAll($event)" />
                </ng-template>
                <ng-template kendoGridCellTemplate let-dataItem>
                    <input class="checkbox-grid" type="checkbox" [checked]="isSelected(dataItem)" (change)="toggleSelection(dataItem)" />
                </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="codeGroupe" [width]="50" title="Code Groupe" class="text-center">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <span style="color: #E09F3E">{{ dataItem.codeGroupe ? dataItem.codeGroupe : "" }}</span>
                </ng-template>
            </kendo-grid-column>
               <kendo-grid-column  field="produitExtensions.codeSpecialEchange"  [width]="50" title="Code Sophatel" class="text-center"></kendo-grid-column>
            <!-- <kendo-grid-column field="codeWinplus" [width]="50" title="Code Winplus" class="text-center">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <span style="color: #E09F3E">{{ dataItem.codeWinplus ? dataItem.codeWinplus : "" }}</span>
                </ng-template>
            </kendo-grid-column> -->
            <kendo-grid-column field="designation" [width]="200" title="Désignation" class="text-start">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{dataItem.designation}}
                </ng-template>
            </kendo-grid-column>
            
            <kendo-grid-column field="prixAchatStd" [width]="50" title="PPH" class="text-center">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{dataItem.prixAchatStd | number: "1.2-2": "Fr-fr"}}
                </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="prixVenteStd" [width]="50" title="PPV" class="text-center">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{dataItem.prixVenteStd | number: "1.2-2": "Fr-fr"}}
                </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="laboratoireLabel" [width]="50" title="Laboratoire" class="text-center"></kendo-grid-column>
            <kendo-grid-column field="dateCreation" [width]="50" title="Date Création" class="text-center">
                <ng-template kendoGridCellTemplate let-dataItem>
                    {{dataItem.dateCreation | date: "dd/MM/yyyy"}}
                </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="produitExtensions.dateEnvoi" title="Date Envoie"  [width]="100">
              <ng-template kendoGridCellTemplate let-dataItem>
                  {{dataItem.produitExtensions.dateEnvoi | date: "dd/MM/yyyy"}}
              </ng-template>
            </kendo-grid-column>
            <ng-template kendoGridNoRecordsTemplate>
                <span>Aucun résultat trouvé.</span>
            </ng-template>
            <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage" let-total="total">
                <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage" [allowPageSizes]="false"
                    [navigation]="navigation" style="width: 100%;" (pageChange)="pageChange($event)"></wph-grid-custom-pager>
            </ng-template>
        </kendo-grid>
    </div>

    <!-- Second Grid: Produits à envoyer -->
    <div class="col-12">
        <div class="page-title-box me-2 mb-1 row">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <h4 class="page-title ps-2">Produits à envoyer</h4>
                <div class="d-flex flex-wrap justify-content-end">
                </div>
            </div>
        </div>
        <ng-container #outlet [ngTemplateOutlet]="sendSelectedGrid"></ng-container>

    </div>
</div>
<ng-template #sendSelectedGrid>
    <kendo-grid     
    #secondGrid
        class="border-grey me-2  ref-grid"
        style="height: 300px; cursor: pointer;"
        [data]="selectedGridData"   [trackBy]="trackByCodeWinplus"
        [rowClass]="rowClass"
    >
        <kendo-grid-column [width]="20" class="text-center">
            <ng-template kendoGridHeaderTemplate>
                <input class="checkbox-grid" type="checkbox" (change)="selectAllSelected($event)" />
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
                <input class="checkbox-grid" type="checkbox" 
                       [checked]="isSelectedInSecondGrid( dataItem)" 
                       (change)="toggleSelectionInSecondGrid($event, dataItem)" />
              </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="codeGroupe" [width]="50" title="Code Groupe" class="text-center">
            <ng-template kendoGridCellTemplate let-dataItem>
                <span style="color: #E09F3E">{{ dataItem.codeGroupe ? dataItem.codeGroupe : "" }}</span>
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column  field="produitExtensions.codeSpecialEchange"  [width]="50" title="Code Sophatel" class="text-center"></kendo-grid-column>
        <kendo-grid-column field="designation" [width]="200" title="Désignation" class="text-start">
            <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem.designation}}
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixAchatStd" [width]="50" title="PPH" class="text-center">
            <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem.prixAchatStd | number: "1.2-2": "Fr-fr"}}
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="prixVenteStd" [width]="50" title="PPV" class="text-center">
            <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem.prixVenteStd | number: "1.2-2": "Fr-fr"}}
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="laboratoireLabel" [width]="50" title="Laboratoire" class="text-center"></kendo-grid-column>
        <kendo-grid-column field="dateCreation" [width]="50" title="Date Création" class="text-center">
            <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem.dateCreation | date: "dd/MM/yyyy"}}
            </ng-template>
        </kendo-grid-column>
                    <kendo-grid-column field="produitExtensions.dateEnvoi" title="Date Envoie"  [width]="50">
              <ng-template kendoGridCellTemplate let-dataItem>
                  {{dataItem.produitExtensions.dateEnvoi | date: "dd/MM/yyyy"}}
              </ng-template>
            </kendo-grid-column>
       
        <ng-template kendoGridNoRecordsTemplate>
            <span>Aucun produit sélectionné.</span>
        </ng-template>
    </kendo-grid>
</ng-template>

<ng-template #configureEnvoiModal let-modal>
    <div class="modal-header">
      <h5 class="modal-title">Configurez l'envoi</h5>
      <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss()"></button>
    </div>
    <div class="modal-body">
      <form>
        <div class="row align-items-center mb-3">
          <label class="form-label">Type d'envoi</label>
          <app-switch
            [formControl]="envoiTypeControl"
            switchClass="primary"
            [elements]="[
              {label: 'Ajout', value: true}, 
              {label: 'Modification', value:false }
            ]"
            (change)="updateAction()"
          >
          </app-switch>
        </div>
        <div class="mb-3">
          <label class="form-label me-2">Sites</label>
          <!-- Add the Select All button here -->
          <div class="d-flex k-gap-1 align-items-center">
              <button
                type="button"
                class="btn btn-sm btn-outline-primary mb-2"
                (click)="selectAllSites()"
              >
                {{showDeselectAllButton ? 'Désélectionner' : 'Sélectionner'}} tous les sites
              </button>
              <button
                type="button"
                *ngIf="showClearButton"
                class="btn btn-sm btn-outline-primary mb-2"
                (click)="deselectAllSites()"
              >
                Vider
              </button>
          </div>
          <div *ngIf="availableSites.length > 0; else noSitesAvailable">
            <select2
              [data]="siteOptions"
              [value]="selectedSites"
              (update)="toggleSiteSelectionFromDropdown($event)"
              [multiple]="true"
              [hideSelectedItems]="true"
              class="form-control bg-white p-0 border-0"
              placeholder="Rechercher et sélectionner des sites"
            >
            </select2>
          </div>
          <ng-template #noSitesAvailable>
            <div class="text-muted">Aucun site disponible.</div>
          </ng-template>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Annuler</button>
      <button
        type="button"
        class="btn btn-primary"
        (click)="confirmEnvoi(modal)"
        [disabled]="!selectedAction || selectedSites.length === 0"
      >
        Envoyer
      </button>
    </div>
  </ng-template>

<ng-template #editDesignationsModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Modifier les désignations trop longues</h5>
    <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss()"></button>
  </div>
  <div class="modal-body">
    <div class="alert alert-warning">
      <i class="fa fa-exclamation-triangle me-2"></i>
      Certains produits ont des désignations de plus de 30 caractères. Veuillez les raccourcir avant l'envoi.
    </div>
    
    <div class="table-responsive">
      <table class="table table-striped">
        <thead>
          <tr>
            <th>Code Groupe</th>
            <th>Désignation</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let product of productsToEdit">
            <td>{{ product.codeGroupe }}</td>
            <td>
              <div class="input-group">
                <input 
                  type="text" 
                  class="form-control" 
                  [class.is-invalid]="editedDesignations[product.codeGroupe]?.length > 30"
                  [class.is-valid]="editedDesignations[product.codeGroupe]?.length <= 30"
                  [value]="editedDesignations[product.codeGroupe]" 
                  (input)="updateDesignation(product.codeGroupe, $event)"
                  maxlength="30"
                />
                <span class="input-group-text" [class.text-danger]="editedDesignations[product.codeGroupe]?.length > 30">
                  {{ getCharCount(editedDesignations[product.codeGroupe]) }}
                </span>
              </div>
            </td>
            <td>
              <button 
                class="btn btn-sm btn-outline-danger" 
                *ngIf="editedDesignations[product.codeGroupe]?.length > 30"
                disabled
              >
                <i class="fa fa-times"></i> Trop long
              </button>
              <button 
                class="btn btn-sm btn-outline-success" 
                *ngIf="editedDesignations[product.codeGroupe]?.length <= 30"
                disabled
              >
                <i class="fa fa-check"></i> Valide
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Annuler</button>
    <button 
      type="button" 
      class="btn btn-primary" 
      [disabled]="!areAllDesignationsValid()"
      (click)="saveDesignations(modal)"
    >
      Enregistrer et continuer
    </button>
  </div>
</ng-template>

<!-- Product Summaries Modal -->
<ng-template #productSummariesModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">
      <i class="fa fa-exclamation-triangle text-warning me-2"></i>
      Produits déjà envoyés
    </h5>
    <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss()"></button>
  </div>
  <div class="modal-body">
    <div class="alert alert-warning">
      <i class="fa fa-info-circle me-2"></i>
      Certains produits ont déjà été envoyés aux sites suivants :
    </div>
    
    <div class="list-group list-group-flush">
      <div class="list-group-item border-0 p-1 px-0" *ngFor="let summary of productSummaries">
        <div class="d-flex align-items-start">
          <i class="fa fa-arrow-right text-primary me-2 mt-1"></i>
          <span>{{ summary }}</span>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-primary" (click)="modal.close()">
      <i class="fa fa-check me-2"></i>
      Fermer
    </button>
  </div>
</ng-template>