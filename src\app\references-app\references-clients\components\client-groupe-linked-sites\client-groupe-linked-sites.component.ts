import { TranscoClientService } from './../../Services/winclient.transcoClient.service';
import { Component, Input, OnInit, OnChanges, SimpleChanges, getPlatform } from '@angular/core';
import { ClientSiteService } from '../../Services/winclient.clientSite.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { ClientSite, ClientSiteFilter } from '../../models/clientSite';
import { ClientGroupe } from '../../models/clientGroupe';
import { Pagination } from 'src/app/references-app/referential/models/pagination.interface';
import { TranscoClientCriteria } from '../../models/transcoClient';
import { UserInputService } from 'src/app/shared/services/user-input.service';
import { SortDescriptor } from '@progress/kendo-data-query';
import { WinClientPlatformService } from '../../Services/winclient.platform.service';
import { PlatformUsage, Platform } from '../../models/platform.model';

@Component({
  selector: 'app-client-groupe-linked-sites',
  templateUrl: './client-groupe-linked-sites.component.html',
  styleUrls: ['./client-groupe-linked-sites.component.scss']
})
export class ClientGroupeLinkedSitesComponent implements OnInit, OnChanges {
  @Input() clientGroupe: ClientGroupe;
  @Input() clientGroupeGridTitle = 'Client Groupe';
  
  linkedClientSites: any = { data: [], total: 0 };
  linkedClientSitesSort: SortDescriptor[] = [];
  clientGroupePlatforms: PlatformUsage;
  allPlatforms: Platform[] = [];
  isLoadingLinkedClientSites = false;
  isLoadingPlatforms = false;
    // Tab management
  activeTab = 1;
  
  linkedClientSitesNavigation: Pagination = {
    skip: 0,
    pageSize: 21
  };

  get IsHaveAtLeastOnePlatform(): boolean {
    return this.allPlatforms.some(platform => this.isPlatformUsed(platform.libelle));
  }

  constructor(
    private clientSiteService: ClientSiteService,
    private platformService: WinClientPlatformService,
    private alertService: AlertService,
    private transcoClientService: TranscoClientService,
    private userInputService: UserInputService // Assuming UserInputService is used for user input handling
  ) { }
  ngOnInit(): void {
    if (this.clientGroupe) {
      this.activeTab = 1;
      this.loadLinkedClientSites();
      this.getAllPlatforms();
      this.getPlatformUsage();
    }
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.clientGroupe && !changes.clientGroupe.firstChange) {
            this.activeTab = 1;
      this.loadLinkedClientSites();
      this.getPlatformUsage();
      this.getAllPlatforms();
    }
  }
  getPlatformUsage(): void {
    if (this.clientGroupe?.codeClientGroupe) {
      this.platformService.getPlatformUsage(this.clientGroupe.codeClientGroupe).subscribe({
        next: (res) => {
          this.clientGroupePlatforms = res;
        },
      });
    }
  }
  getAllPlatforms(): void {
    this.isLoadingPlatforms = true;
    this.platformService.getAllPlatforms().subscribe({
      next: (res) => {
        this.allPlatforms = res;
      },
      complete: () => {
        this.isLoadingPlatforms = false;
      }
    });
  }

  isPlatformUsed(platformId: string): boolean {
    if (!this.clientGroupePlatforms?.plateformes) {
      return false;
    }
    return this.clientGroupePlatforms.plateformes.some(p => p['plateforme'] === platformId);
  }

  loadLinkedClientSites(page=0) {
      this.linkedClientSitesNavigation.skip = page * this.linkedClientSitesNavigation.pageSize;

      const clientSiteFilter =  new ClientSiteFilter({
        codeGroupe: String(this.clientGroupe.codeClientGroupe),
       })
      const paginationParams = {
        page,
        size: this.linkedClientSitesNavigation.pageSize,
        sort: this.linkedClientSitesSort[0] ? `${this.linkedClientSitesSort[0]?.field},${this.linkedClientSitesSort[0]?.dir || 'asc'}` : '',
      }
      this.isLoadingLinkedClientSites = true;
      this.clientSiteService.searchClientSite(clientSiteFilter, paginationParams).subscribe({
        next: (res) => {
          console.log("Sites associés:", res);
          this.linkedClientSites.data = res.content;
          this.linkedClientSites.total = res.totalElements;
        },
        complete: () => {
          this.isLoadingLinkedClientSites = false;
        }
      });
    }


  detacherClientSite(clientSite:ClientSite): void {


    this.userInputService.confirm("Dissocier le site",
      `Êtes-vous sûr de vouloir dissocier le site ${clientSite.cliNomPhar || '(nom vide)'} du client groupe  ${this.clientGroupe.nomPharmacien} ?`,
    "Oui, Détacher",
    "Annuler"
    ).then((confirmed) => {
      if (confirmed) {

            const transcoClientCriteria = new TranscoClientCriteria({
                codeClientSite: clientSite.cliCode,
                codeSite: clientSite.codeSite,  
                      
              });
              
              this.transcoClientService.searchTranscoClient(transcoClientCriteria).subscribe({
                next: (value) => {
                  if (value && value.length > 0) {
                    const transcoClient = value[0];
                    this.transcoClientService.deleteTranscoClient(transcoClient.id).subscribe({
                      next: () => {
                        this.alertService.success("Le site a été dissocié avec succès.");
                        this.loadLinkedClientSites();
                      }
                    });
                  }
                }
              });

      }}).catch(()=>{})

  }

  linkedClientSitesPageChange(skip:number): void {
    this.linkedClientSitesNavigation.skip =skip;
    const page = Math.ceil(skip / this.linkedClientSitesNavigation.pageSize);
    this.loadLinkedClientSites(page);
  }

  linkedClientSitesSortChange(sort: SortDescriptor[]): void {
    this.linkedClientSitesSort = sort;
    this.linkedClientSitesNavigation.skip = 0; // Reset pagination on sort change
    this.loadLinkedClientSites();
  }


}
