
interface IPlatform {
    id: number;
    libelle: string;
}

interface ILigneDetailPlateform{
    codeClient : number;
    isEnrolled: boolean;
}

interface IPlateformUsageCriteria {
    listCodeClientGroupe: ILigneDetailPlateform[];
    plateformName: string;
}

export class PlateformUsageCriteria implements IPlateformUsageCriteria {
    listCodeClientGroupe:LigneDetailPlateform[];
    plateformName: string;
    constructor(criteria: Partial<IPlateformUsageCriteria>) {
        Object.assign(this, criteria);
    }
}

export class LigneDetailPlateform implements ILigneDetailPlateform {
    codeClient: number;
    isEnrolled: boolean;

    constructor(detail: Partial<ILigneDetailPlateform>) {
        Object.assign(this, detail);
    }
}

export class Platform implements IPlatform {
    id: number;
    libelle: string;

    constructor(platform: Partial<IPlatform>) {
        Object.assign(this, platform);
    }
}

export type PlatformUsage = {
    plateformes: Platform[];
    used: boolean;
}


