import { tap } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment as env } from 'src/environments/environment';
import { Page } from '../../referential/models/Page/page.model';
import { WinClientVille } from '../models/ville';
import { BehaviorSubject, of } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class WinclientVilleService {

  private _villes$ = new BehaviorSubject<WinClientVille[]>([]);
  villes$ = this._villes$.asObservable();



  constructor(private http: HttpClient) { }

  initializeVilles() {
    this.getAllVilles().pipe(
      tap((response) => {
        this._villes$.next(response.content);
      })
    ).subscribe();
  }

  getAllVilles() {
    return this.http.get<Page<WinClientVille>>(`${env.winclient_base_url}/api/winclient/villes`,{
      params:{paginated:false}
    });
  }

  getAllVillesPaginated({page =0}:{page:number}) {
    return this.http.get<Page<WinClientVille>>(`${env.winclient_base_url}/api/winclient/villes`, { 
        params:{paginated:false}
     });
  }

  getVilleById(id: number) {
    return this.http.get<WinClientVille>(`${env.winclient_base_url}/api/winclient/villes/${id}`);
  }

  createVille(ville: WinClientVille) {
    return this.http.post<WinClientVille>(`${env.winclient_base_url}/api/winclient/villes`, ville);
  }

  updateVille(id:string,ville: WinClientVille) {
    return this.http.put<WinClientVille>(`${env.winclient_base_url}/api/winclient/villes/${id}`, ville);
  }

  deleteVille(id: string) {
    return this.http.delete<WinClientVille>(`${env.winclient_base_url}/api/winclient/villes/${id}`);
  }

}