
enum TypeInternet {
    FIBRE = 'FIBRE',
    ADSL = 'ADSL',
    SANS = 'SANS'
}

enum WinpharmVersion {
    V4_6 = "V4_6",
    V6_2 = "V6_2",
    V7_1 = "V7_1",
    V8 = "V8",
    V9 = "V9",
    V10_04 = "V10_04",
    V10_2 = "V10_2",
    V11_41 = "V11_41",
    V12_3 = "V12_3",
    V13_1 = "V13_1",
    V13_31 = "V13_31",
    V13_32 = "V13_32",
    V13_34 = "V13_34",
    V13_35 = "V13_35",
    V14 = "V14",
    V15_1 = "V15_1",
    V15_2 = "V15_2",
    V15_367 = "V15_367",
    V16_0 = "V16_0",
    V16_1 = "V16_1",
    V16_2 = "V16_2",
    V17_1_1 = "V17_1_1",
    V17_2 = "V17_2",
    V18 = "V18",
    V18_1 = "V18_1",
    V18_2 = "V18_2"
}

enum OsVersion {
    WINDOWS_7 = 'WINDOWS_7',
    WINDOWS_8 = 'WINDOWS_8',
    WINDOWS_10 = 'WINDOWS_10',
    WINDOWS_11 = 'WINDOWS_11',
    WINDOWS_SERVER_2012 = 'WINDOWS_SERVER_2012',
    WINDOWS_SERVER_2016 = 'WINDOWS_SERVER_2016',
    WINDOWS_SERVER_2019 = 'WINDOWS_SERVER_2019',
    AUTRE = 'AUTRE'
}



interface IInfoTechniqueCriteria {
    cliCode: string;
    codeSite: number; transcoWinplus: boolean;
    typeInternet: TypeInternet;
    versionOs: OsVersion;
    versionWinpharm: WinpharmVersion;
    nomPharmacien: string;
    raisonSociale: string;
}




export class InfoTechniqueCriteria implements IInfoTechniqueCriteria {
    cliCode: string; codeSite: number;
    transcoWinplus: boolean; typeInternet: TypeInternet;
    versionOs: OsVersion; versionWinpharm: WinpharmVersion;
    nomPharmacien: string;
    raisonSociale: string;

    constructor(criteria?: Partial<IInfoTechniqueCriteria>) {
        Object.assign(this, criteria);
    }
}


interface ITechniqueInfo {
    cliCode: string;
    codeSite: number;
    dateMaj: string;
    nomPharmacien: string;
    raisonSociale: string;
    transcoWinplus: boolean;
    typeInternet: TypeInternet;
    versionOs: OsVersion;
    versionWinpharm: WinpharmVersion;
    gsmResponsable: string;
}

export class TechniqueInfo implements ITechniqueInfo {
    cliCode: string;
    codeSite: number;
    dateMaj: string;
    nomPharmacien: string;
    raisonSociale: string;
    transcoWinplus: boolean;
    typeInternet: TypeInternet;
    versionOs: OsVersion;
    versionWinpharm: WinpharmVersion;
    gsmResponsable: string;

    constructor(data?: Partial<ITechniqueInfo>) {
        Object.assign(this, data);
    }
}






export { TypeInternet, WinpharmVersion, OsVersion };