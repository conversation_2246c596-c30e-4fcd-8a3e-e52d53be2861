<div class="custom-drawer-container" 
    *ngIf="initialized"
    [style.visibility]="isOpen ? 'visible' : 'hidden'"
    [style.opacity]="isOpen ? '1' : '0'"
    [style.pointer-events]="isOpen ? 'auto' : 'none'"
    [style.z-index]="zIndex"
    [attr.aria-hidden]="!isOpen"
    role="dialog"
    aria-modal="true"
    [attr.aria-labelledby]="hasCustomTitle ? null : 'drawer-title'"
    [attr.aria-label]="hasCustomTitle ? title : null">
  
  <div class="custom-drawer-content bg-white" 
      [class.shake-effect]="isShaking"
      [style.width]="width"
      [style.transform]="isOpen ? 'translateX(0)' : position === 'right' ? 'translateX(100%)' : 'translateX(-100%)'" 
      [style.right]="position === 'right' ? '0' : 'auto'"
      [style.left]="position === 'left' ? '0' : 'auto'"
      (click)="stopPropagation($event)"
      #drawerContent>
    
    <!-- Header -->
    <div class="modal-header">
      <h5 class="modal-title" 
          style="line-height: 1;"
          [id]="hasCustomTitle ? null : 'drawer-title'">
        <ng-content select="[drawer-title]"></ng-content>
        <span *ngIf="!hasCustomTitle">{{ title }}</span>
      </h5>
      <button type="button" 
              class="cross-button" 
              data-bs-dismiss="modal" 
              aria-label="Close drawer"
              [attr.aria-describedby]="'drawer-title'"
              (click)="close()">
        <i class="mdi mdi-close" aria-hidden="true"></i>
      </button>
    </div>
      <!-- Body -->
    <main class="d-flex flex-column" 
         style="height: calc(100% - 46px); overflow-y: auto;scrollbar-width: thin; scrollbar-color: #888 #f1f1f1;"
         tabindex="-1">
      <ng-content></ng-content>
    </main>
    
    <!-- Footer (optional) -->
      <ng-content select="[drawer-footer]" ></ng-content>
   </div>
</div>
