import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import {  ProduitSiteCriteriaForm } from '../../../models/produit/ref-produits/ProduitSiteCriteria.model';
import { ProduitService } from '../../../Services/produit/produit.service';
import { Pagination } from 'src/app/references-app/referential/models/pagination.interface';
import { GridDataResult, RowClassArgs } from '@progress/kendo-angular-grid';
import { CodeSites } from './data';
import { Observable, of, OperatorFunction, Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap,filter, map, retry } from 'rxjs/operators';
import { UserInputService } from 'src/app/shared/services/user-input.service';
import { BootstrapColorClasses } from 'src/app/shared/confirm/confirm.component';
import { ProduitSite } from '../../../models/produit/ref-produits/produitSite.model';
import { AlertService } from 'src/app/shared/services/alert.service';
import { ActivatedRoute, Router } from '@angular/router';
import moment from 'moment-timezone';
import { ProduitWinplusCriteria } from '../../../models/produit/ref-produits/produitsWinplus.model';
import { Labo } from '../../../models/produit/ref-produits/labo.model';
import { Dci } from '../../../models/produit/ref-produits/dci.model';
import { Categorie } from '../../../models/produit/ref-produits/category.model';
import { FormeProduit } from '../../../models/produit/ref-produits/forme.model';
import { TAPCriteria } from '../../../models/produit/ref-produits/tap.model';
import { ProduitWinplus } from '../../../models/produit/ref-produits/produitWinplus.mode';
import { SortDescriptor } from '@progress/kendo-data-query';

@Component({
  selector: 'app-list-produit-winplus',
  templateUrl: './list-produit-winplus.component.html',
  styleUrls: ['./list-produit-winplus.component.scss']
})
export class ListProduitWinplusComponent implements OnInit {
  winplusProducts: GridDataResult = {
    data: [],
    total: 0
  };
  filterForm: FormGroup;
  categories: Categorie[] = [];
  produitForms: FormeProduit[] = [];
  sort : SortDescriptor[] = [];
  showFilter = false;
  navigation: Pagination = {
    skip: 0,
    pageSize: 25,
    sortField:'',
    sortMethod: '',
  };

  produitSiteCriteria: Partial<ProduitWinplusCriteria> = {
    categorieId :[2], // Default to 'Medicaments'
  };
  clickedItem: any = {};
  private produitGroupeSearchSubject = new Subject<string>();
  private productWinplusSearchSubject = new Subject<string>();
  private subscriptions = new Subscription();

  constructor(
    private fb: FormBuilder,
    private produitService: ProduitService,
    private userInpService: UserInputService,
    private alertService: AlertService,
    private router: Router,
    private route: ActivatedRoute
  ) { }

  ngOnInit() {
    this.initFilterForm();
    this.loadAllCategories();
    this.loadAllProduitForms();
    this.getWinplusProducts();
 
  }



  initFilterForm() {
    this.filterForm = this.fb.group({
      designation: [null],
      codeWinplus: [null],
      codeBarre: [null],
      categorie: [null],
      dci: [null],
      labo: [null],
      forme: [null],
    });
  }

  loadListProduitsSite() {
    console.log('Loading products with criteria:', this.produitSiteCriteria);
  }

  loadAllCategories(){
    this.produitService.getAllCategories().subscribe({
      next: (res) => {
        this.categories = res;
      },
    });
  }

  loadAllProduitForms() {
    this.produitService.getAllForms().subscribe({
      next: (res) => {
        this.produitForms = res;
      },
    });
  }

  filterSubmit() {
    this.showFilter = false;
    const filterValues = this.filterForm.value;
    const criteria = this.filterToCriteria(filterValues);
    const cleanedCriteria = this.cleanEmptyValues(criteria);
    this.produitSiteCriteria = cleanedCriteria;
    this.navigation.skip = 0;
    this.getWinplusProducts();
  }

  filterClear(){
    this.filterForm.reset();
    this.filterSubmit();
  }

  pageChange(skip: number) {
    this.navigation.skip = skip;
    this.getWinplusProducts();
  }

  filterToCriteria(filterValues: any): ProduitWinplusCriteria {
    const criteria =  {
      designation: filterValues.designation,
      codeWinplus: filterValues.codeWinplus,
      codeBarre: filterValues.codeBarre,
      categorieId: filterValues.categorie?.id ? [filterValues.categorie?.id] : null,
      dciId: filterValues.dci?.id,
      laboId: filterValues.labo?.id,
      formeId: filterValues.forme?.id,
     }
    return criteria;
  }

  cleanEmptyValues<T>(filterValues: T): T {
    const cleanedValues = { ...filterValues };

    Object.keys(cleanedValues).forEach(key => {
      if (typeof cleanedValues[key] === 'string') {
        cleanedValues[key] = cleanedValues[key].trim();
      }
    });
    Object.keys(cleanedValues).forEach(key => {
      if (cleanedValues[key] === null || cleanedValues[key] === undefined || typeof cleanedValues[key] === 'string' && cleanedValues[key].trim() === '') {
        delete cleanedValues[key];
      }
    });
    // trim all string values
    return cleanedValues;
  }

  getWinplusProducts() {
    this.produitService.getProduitsWinplusByCriteria(this.navigation, this.produitSiteCriteria).subscribe((res) => {
      this.winplusProducts.data = res?.content;
      this.winplusProducts.total = res?.totalElements;
    });
  }

    searchProductWinplusByTap(){
      const formCriteria = this.cleanEmptyValues(this.filterToCriteria(this.filterForm.value));
      if (!formCriteria.designation && !formCriteria.codeBarre) {
        this.alertService.warning("Veuillez saisir au moins un critère de recherche (designation ou code barre)", "Recherche Produit Winplus");
        return ;
      }

      const tapCriteria = new TAPCriteria({
        designation : formCriteria.designation,
        barcode: formCriteria.codeBarre,
      })
      // Check if the tapCriteria is empty
      this.produitService.searchProduitByAssistant([tapCriteria])
        .pipe(
          retry(2) // Retry up to 2 times if the request fails
        )
        .subscribe({
          next: (res) => {
            const listProduitWinplus = res.associations.map((association) => new ProduitWinplus({
              designation: association.nom_base_winplus,
              codeWinplus: association.code_prd_winplus,
              codeGroupe: association.code_groupe,
              prixVenteStd: association.ppv_winplus,
              prixAchatStd: association.pph_winplus,
              codeBarre: association.code_barre_win,
            }))
            this.winplusProducts.data = listProduitWinplus;
            this.winplusProducts.total = listProduitWinplus.length;
          },
          complete: () => {
            this.toggleFilter(false);
        }
        })
    }

 

  createProductGroupeFromWinplus(produitWinplus: ProduitWinplus) {
    this.clearClickedItem();
    produitWinplus['clicked'] = true;
    this.userInpService.confirm("Crée le produit ?",
      `vous êtes sur le point de créer le produit Groupe: ${produitWinplus?.designation} a partir d'un produit Winplus`,
      "oui Crée", "non", BootstrapColorClasses.success).then((res) => {
      if (res) {
        const produitSite = this.buildProduitSiteFromWinplus(produitWinplus);
      this.produitService.updateOrCreateProduitGroupe(produitSite).subscribe({
        next: (res) => {
          this.alertService.success("Produit Groupe Créé avec succès", "Produit Groupe");
          // this.router.navigate(['/references/ref-produits/produits/produits-groupes'], { queryParams: { codeGroupe: res.codeGroupe } });
        }
        , error: (err) => {
          this.alertService.error("Erreur lors de la création du produit Groupe", "Produit Groupe");
        }
    });
      }}).catch(()=>{})
   
  }


  buildProduitSiteFromWinplus(produitWinplus: ProduitWinplus) {
    const produitSite = new ProduitSite({
      codeSite: 0,
      laboratoireLabel: produitWinplus.labo?.raisonSociale,
      formeGalenique: produitWinplus.forme?.libelleForme,
      categorie: produitWinplus.categorie?.libelleCategorie,
      codeWinplus: produitWinplus.codeWinplus,
      codeGroupe: produitWinplus.codeGroupe,
      designation: produitWinplus.designation,
      codeBarre: produitWinplus.codeBarre,
      prixVenteStd: produitWinplus.prixVenteStd,
      prixAchatStd: produitWinplus.prixAchatStd,
      dci: produitWinplus.dci?.libelleDci,
    })
    return produitSite;
  }


  toggleFilter(forcedValue?: boolean) {
    this.showFilter = forcedValue !== undefined ? forcedValue : !this.showFilter;

  }


  clearClickedItem() {
    this.winplusProducts.data.forEach(item => {
      delete item['clicked'];
    });
  }

  compareFn(a, b) {
    return a == b;
  }

  rowClass(args: RowClassArgs) {
    if (args.dataItem?.clicked) {
      return { 'highlight-row-clicked': true };
    }
    return '';
  }


  // Function to get similarity score between two strings mean the one start with term will show first
  getSimilarityScore(input: string, target: string): number {
      input = input.toLowerCase();
      target = target.toLowerCase();

      if (target.startsWith(input)) return 0; // Best match
      if (target.includes(input)) return 1;   // Medium match
      return 2; // Worst match (not included)
  }


    searchDciTypeahead: OperatorFunction<string, readonly Dci[]> = (
      text$: Observable<string>
    ) =>
      text$.pipe(
        debounceTime(200),
        distinctUntilChanged(),
        filter((term) => term.length >= 2),
        switchMap((term:string) =>
          this.produitService.searchDci({ pageSize: 21, skip: 0 }, { libelle: term })
            .pipe(
              map(response => {
            const items: Dci[] = response.content || [];
            return items.sort((a, b) =>
              this.getSimilarityScore(term, a.libelleDci) - this.getSimilarityScore(term, b.libelleDci)
            );
          })
        )
        )
      )

  searchLaboTypeahead: OperatorFunction<string, readonly Labo[]> = (
      text$: Observable<string>
    ) =>
      text$.pipe(
        debounceTime(200),
        distinctUntilChanged(),
        filter((term) => term.length >= 2),
        switchMap((term:string) =>
          this.produitService.searchLabo({ pageSize: 21, skip: 0 }, { raisonSociale: term ,typeFrn: 'L'})
            .pipe(
              map(response => {
            const items: Labo[] = response.content || [];
            return items.sort((a, b) =>
              this.getSimilarityScore(term, a.raisonSociale) - this.getSimilarityScore(term, b.raisonSociale)
            );
          })
        )
        )
      )

  searchFormeTypeahead: OperatorFunction<string, readonly FormeProduit[]> = (
      text$: Observable<string>
    ) =>
      text$.pipe(
        debounceTime(200),
        distinctUntilChanged(),
        filter((term) => term.length >= 2),      
        map((term: string) =>{
          const items = this.produitForms.filter(forme => forme.libelleForme.toLowerCase().includes(term.toLowerCase()))
          return items.sort((a, b) =>
            this.getSimilarityScore(term, a.libelleForme) - this.getSimilarityScore(term, b.libelleForme)
          );
        })

      )

  formatterLabo = (localite: Labo) =>  localite.raisonSociale;
  formatterDci = (localite: Dci) =>  localite.libelleDci;
  formatterForme = (forme: FormeProduit) =>  forme.libelleForme;
  
  compareCategories = (a: Categorie, b: Categorie) => a?.id === b?.id || a == b;


  sortChange(sort: SortDescriptor[]) {
    this.sort = sort;
    if (this.sort.length > 0) {
      this.navigation.sortField = this.sort[0].field;
      this.navigation.sortMethod = this.sort[0].dir;
    } else {
      this.navigation.sortField = '';
      this.navigation.sortMethod = '';
    }
    this.getWinplusProducts();
  }

}
