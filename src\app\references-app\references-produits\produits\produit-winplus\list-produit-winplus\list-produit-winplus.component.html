<app-custom-drawer
[title]="'Filtrer Produit Winplus'"
(isOpenChange)="toggleFilter($event)"
[isOpen]="showFilter"
>
 <div class="p-2">
   <form id="filterForm" [formGroup]="filterForm" (ngSubmit)="filterSubmit()"   appFocusTrap>
        <div class="flex-grow-1">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label">Designation</label>
                <input type="search" class="form-control" formControlName="designation" appAutoFocus>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label">Code winplus</label>
                <input type="search" class="form-control" formControlName="codeWinplus">
              </div>
            </div>
   
          </div>
                  <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label">Categorie</label>
                <select class="form-select" formControlName="categorie" [compareWith]="compareCategories">
                  <option [ngValue]="null">-- Sélectionner une catégorie --</option>
                  <option *ngFor="let categorie of categories" [ngValue]="categorie">{{categorie.libelleCategorie}}</option>
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label">Code Barre</label>
                <input type="search" class="form-control" formControlName="codeBarre">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label">Forme</label>
                <input type="search" class="form-control"
                [ngbTypeahead]="searchFormeTypeahead"
                [resultFormatter]="formatterForme"
                [inputFormatter]="formatterForme"
                [editable]="false"
                formControlName="forme">
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label">Labo</label>
                <input type="search" class="form-control"
                [ngbTypeahead]="searchLaboTypeahead"
                [resultFormatter]="formatterLabo"
                [inputFormatter]="formatterLabo"
                [editable]="false"
                formControlName="labo">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-12">
              <div class="mb-3 position-relative dc-search">
                <label class="form-label">Dci</label>
                <input type="search" class="form-control" formControlName="dci"
                [ngbTypeahead]="searchDciTypeahead"
                [resultFormatter]="formatterDci"
                [inputFormatter]="formatterDci"
                [editable]="false"
                >
              </div>
            </div>
          </div>
        </div>
      </form>
 </div>
    <div class="modal-footer justify-content-start bg-white" drawer-footer>
      <div class="d-flex flex-wrap gap-2" >
        <button class="btn btn-primary" tabindex="-1" type="submit" form="filterForm">
           <i class="mdi mdi-magnify"></i>
          Rechercher</button>
        <button class="btn btn-warning" tabindex="-1" type="button" (click)="searchProductWinplusByTap()">
          <i class="mdi mdi-lightbulb-on-outline"></i>
          Rechercher avancée
        </button>
        <button class="btn btn-dark" tabindex="-1" type="button" (click)="filterClear()">
          <i class="mdi mdi-filter-remove-outline"></i>
          Vider</button>
      </div>
    </div>
</app-custom-drawer>

<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">Produit Winplus</h4>
      <div class="d-flex align-content-center align-items-center gap-1">
      <button
        type="button"
        class="btn btn-dark me-2 py-1 d-flex justify-content-center align-items-center"
        (click)="toggleFilter(true)"
        [attr.aria-expanded]="showFilter ? 'true' : 'false'"
        [attr.aria-controls]="showFilter ? 'filter' : ''"
      >
        <i class="mdi mdi-filter pe-1"></i>
        <span>Filtrer</span>
      </button>
      </div>

    </div>
  </div>
</div>





<div class="pe-2">
  <kendo-grid class="border-grey mt-2 have-winplus-association-grid  header-wrap content-wrap fournisseur-product-grid custom-sort-grid"  
    [data]="winplusProducts" style="height: calc(100vh - 130px);border-radius: 10px;" 
    [pageable]="true"
    [pageSize]="navigation.pageSize"
    [skip]="navigation.skip"
    [rowClass]="rowClass"
    [sortable]="true"
    [sort]="sort"
    (sortChange)="sortChange($event)"
    >
    <kendo-grid-column field="codeWinplus" title="Code winplus" [width]="100">
       <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"    [type]="'numeric'"
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
    </kendo-grid-column>
     <kendo-grid-column field="codeGroupe" title="Code Groupe" [width]="70">
       <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"    [type]="'numeric'"
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
     </kendo-grid-column>
    <kendo-grid-column field="designation" title="Designation" [width]="300">
       <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="labo.raisonSociale" title="Laboratoire" [width]="100">
       <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
    </kendo-grid-column>
    
    <kendo-grid-column field="prixAchatStd" title="PPH" [width]="100" class="text-end">
       <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"   [type]="'numeric'"
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
          {{dataItem.prixAchatStd  | number: "1.2-2": "Fr-fr"}}
        </ng-template>

    </kendo-grid-column>
    <kendo-grid-column field="prixVenteStd" title="PPV" [width]="100" class="text-end">
       <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  [type]="'numeric'"
              [active]="navigation.sortField === column.field" 
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
          <!-- fr -->
           {{dataItem.prixVenteStd  | number: "1.2-2": "Fr-fr"}}
        </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="codeBarre" title="Code Barre" [width]="100" class="text-end">
       <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
              <app-grid-sort-header [title]="column.title"  
              [active]="navigation.sortField === column.field"
              [direction]="navigation.sortMethod"></app-grid-sort-header>
          </ng-template>
    </kendo-grid-column>
    <kendo-grid-column   title="Action" [width]="100" > 
      <ng-template kendoGridCellTemplate let-dataItem>
          <app-action-icon [icon]="'plus'" title="Créer un Produit Groupe depuis ce produit winplus" [extendClass]="'circle-lg'" (click)="createProductGroupeFromWinplus(dataItem)" ></app-action-icon>
      </ng-template>
    </kendo-grid-column>


    <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
    let-total="total">
    <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"  [allowPageSizes]="true"
      [navigation]="navigation" style="width: 100%;"
      (pageChange)="pageChange($event)"></wph-grid-custom-pager>
  </ng-template>
  </kendo-grid>
</div>

