interface IClientSite {
    cliAdresse: string;
    cliCategorie: string;
    cliCode: number;
    cliLocalite: string;
    cliNomPhar: string;
    cliPatente: string;
    cliRaiSoc: string;
    cliTel: string;
    cliVille: string;
    dateMaj: string;  
    estActifSite: boolean;
    estSignale: string;
    id: number;
    codeSite: number;
    tag: string;
  };



export class ClientSite implements IClientSite{
    cliAdresse: string;
    cliCategorie: string;
    cliCode: number;
    cliLocalite: string;
    cliNomPhar: string;
    cliPatente: string;
    cliRaiSoc: string;
    cliTel: string;
    cliVille: string;
    dateMaj: string;  
    estActifSite: boolean;
    estSignale: string;
    id: number;
    codeSite: number;
    tag: string;

    constructor(clientSite:Partial<IClientSite>){
        Object.assign(this,clientSite)
    }

}



interface IClientSiteFilter {
    codeGroupe?: string;
    codeLocal?: number;
    raisonSociale?: string;
    nomPharmacien?: string;
    codeSite?: number; // Assuming it will store the selected site value
    ville?: string;
    isGrouped?: null | boolean; // Radio button options
    signalement?: 'Tous' | 'Signalé' | 'Non Signalé'; // Radio button options
    nouveauClient?: boolean; // Checkbox
    dateDebut?: string; // Date input
    dateFin?: string; // Date input
    tagGroupement?: string;
    tagClient?: string;
}

export class ClientSiteFilter implements IClientSiteFilter{
    codeGroupe?: string;
    codeLocal?: number;
    raisonSociale?: string;
    nomPharmacien?: string;
    codeSite?: number; // Assuming it will store the selected site value
    ville?: string; 
    isGrouped?: null | boolean; // Radio button options
    signalement?: 'Tous' | 'Signalé' | 'Non Signalé'; // Radio button options
    nouveauClient?: boolean; // Checkbox
    dateDebut?: string; // Date input
    dateFin?: string; // Date input
    tagGroupement?: string;
    tagClient?: string;
 
    constructor(clientSiteFilter:Partial<IClientSiteFilter>){
        Object.assign(this,clientSiteFilter)
     }
}