import { Pipe, PipeTransform } from '@angular/core';
import { WinclientSitesService } from 'src/app/references-app/references-clients/Services/winclient.sites.service';

@Pipe({
  name: 'clientSitePipe'
})
export class ClientSitePipe implements PipeTransform {

//  from winclient.sites.service.ts load all sites
sites : { code_site: number; nom: string; code: string }[] = [];
constructor(
  private winclientSitesService: WinclientSitesService
) { 
  winclientSitesService.sites$.subscribe(sites => {
    this.sites = sites.map(site => ({
      code_site: site.id,
      nom: site.libelleLong,
      code: site.libelleCourt
    }));
  }
  );
}



  transform(value: any, args?: any): any {
    if (value !== null && value !== undefined) {
      const site = this.sites.find(site => site.code_site === value);
      return site ? `${site.nom}` : value;
    }
    return value;
  }

}
