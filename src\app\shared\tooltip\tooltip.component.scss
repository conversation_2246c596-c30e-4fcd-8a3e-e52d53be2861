.tooltip {
  position: fixed;
  z-index: 9999;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
  pointer-events: auto;
  transform-origin: center;
  transition: opacity 0.2s ease, transform 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  max-width: calc(100vw - 40px);
  max-height: calc(100vh - 40px);
  overflow-wrap: break-word;
  hyphens: auto;
  box-sizing: border-box;
  visibility: hidden;
  opacity: 0;

  &.tooltip--visible {
    visibility: visible !important;
    opacity: 1 !important;
  }

  &.tooltip--dark {
    background-color: #1a1a1a;
    color: #ffffff;
    border: 1px solid #333;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  &.tooltip--light {
    background-color: #ffffff;
    color: #1a1a1a;
    border: 1px solid #e0e0e0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &.tooltip--custom {
    background-color: var(--tooltip-bg, #1a1a1a);
    color: var(--tooltip-color, #ffffff);
    border: 1px solid var(--tooltip-border, #333);
    box-shadow: var(--tooltip-shadow, 0 4px 12px rgba(0, 0, 0, 0.3));
  }

  &.tooltip--animated {
    animation: tooltipFadeIn 0.2s ease forwards;

    &[data-animation="scale"] {
      transform: scale(0.8);
      animation: tooltipScaleIn 0.2s ease forwards;
    }

    &[data-animation="slide"] {
      transform: translateY(-10px);
      animation: tooltipSlideIn 0.2s ease forwards;
    }
  }
}

.tooltip-content {
  position: relative;
  z-index: 1;
}

.tooltip-arrow {
  position: absolute;
  width: 8px;
  height: 8px;
  transform: rotate(45deg);
  z-index: 0;
}

.tooltip--dark .tooltip-arrow {
  background-color: #1a1a1a;
  border: 1px solid #333;
}

.tooltip--light .tooltip-arrow {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
}

.tooltip--custom .tooltip-arrow {
  background-color: var(--tooltip-bg, #1a1a1a);
  border: 1px solid var(--tooltip-border, #333);
}

.tooltip[data-placement^="top"] .tooltip-arrow {
  border-bottom: none;
  border-right: none;
}

.tooltip[data-placement^="bottom"] .tooltip-arrow {
  border-top: none;
  border-left: none;
}

.tooltip[data-placement^="left"] .tooltip-arrow {
  border-top: none;
  border-right: none;
}

.tooltip[data-placement^="right"] .tooltip-arrow {
  border-bottom: none;
  border-left: none;
}

@keyframes tooltipFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes tooltipScaleIn {
  from { 
    opacity: 0; 
    transform: scale(0.8); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}

@keyframes tooltipSlideIn {
  from { 
    opacity: 0; 
    transform: translateY(-10px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@media (max-width: 768px) {
  .tooltip {
    max-width: calc(100vw - 20px) !important;
    max-height: calc(100vh - 20px) !important;
    font-size: 13px;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .tooltip {
    max-width: calc(100vw - 16px) !important;
    max-height: calc(100vh - 16px) !important;
    font-size: 12px;
    padding: 4px 8px;
  }
}
