<app-custom-drawer
[width]="'600px'"
[isOpen]="isDrawerFilterOpen"
[title]="'Filtrer Client Site'"
(isOpenChange)="isDrawerFilterOpen = $event"
>
<div class="p-2" drawer-body>
     <form id="clientSiteFilterForm" [formGroup]="clientSiteFilterForm" (ngSubmit)="onFilterSubmit()" appFocusTrap>
      <div class="flex-grow-1">
      <div class="row">
        <div class="col-12 col-sm-6">
          <label for="codeGroupe" class="form-label">Code Groupe</label>
          <input id="codeGroupe" formControlName="codeGroupe" class="form-control" id="codeGroupe">
        </div>
        <div class="col-12 col-sm-6">
          <label for="codeLocal" class="form-label">Code Local</label>
          <input id="codeLocal" formControlName="codeLocal" class="form-control">
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-sm-6">
          <label for="raisonSociale" class="form-label">Raison Social</label>
          <input id="raisonSociale" formControlName="raisonSociale" class="form-control">
        </div>
        <div class="col-12 col-sm-6">
          <label for="nomPharmacien" class="form-label">Nom Pharmacien</label>
          <input id="nomPharmacien" formControlName="nomPharmacien" class="form-control">
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-sm-6">
          <label for="sites" class="form-label">Site</label>
          <select id="sites" formControlName="site" class="form-select">
            <option [ngValue]="null">Select Site</option>
            <option *ngFor="let site of sites" [ngValue]="site">{{ site.libelleLong}}({{site.libelleCourt}})</option>
            </select>
        </div>
        <div class="col-12 col-sm-6">
          <label for="ville" class="form-label">Ville</label>
          <input [ngbTypeahead]="searchVilleTypeahead" class="form-control" id="ville"
          formControlName="ville" [inputFormatter]="formatter"
          placeholder="taper ville nom"
          [resultFormatter]="formatter">
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-12 d-flex flex-column flex-sm-row align-items-start align-items-sm-center mb-2 mb-sm-0">
          <label for="sites" class="form-label me-2 mb-1 mb-sm-0">Groupemets</label>
          <div class="w-100">
                  <app-switch
          [elements]='[{ label: "Tous", value:null},{ label: "Groupé", value:true},{ label: "Non Groupé", value:false}]'
          formControlName="isGrouped" name="groupement" [disabled]="false"
          [switchClass]="'info'"></app-switch>
          </div>
    
        </div>
        <div class="col-12 d-flex flex-column flex-sm-row align-items-start align-items-sm-center mt-2">
          <label for="signalement" class="form-label me-2 mb-1 mb-sm-0">Signalement</label>
          <div class="w-100">
                  <app-switch
          [elements]='[{ label: "Tous", value:null},{ label: "Signale", value:"O"},{ label: "Non Signale", value:"N"}]'
          formControlName="signalement" name="signalement" [disabled]="false"
          [switchClass]="'info'"></app-switch>
          </div>
    
        </div>
      
      </div>
      <div class="row mt-2">
        <div class="col-12  mb-2">
          <label for="nouveauClient" class="me-1">Nouveau Client ?</label>
          <input type="checkbox" class="form-check-input" formControlName="nouveauClient">
        </div>
        <div class="col-12 col-sm-6 mb-2 mb-sm-0">
          <label for="dateDebut" class="form-label">Date Debut</label>
          <app-date-picker formControlName="dateDebut"></app-date-picker>
        </div>
        <div class="col-12 col-sm-6">
          <label for="dateFin" class="form-label">Date Fin</label>
          <app-date-picker formControlName="dateFin" ></app-date-picker>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-12 col-sm-6">
          <label for="tagGroupement">Tag Groupement</label>
          <input type="text" name="tagGroupement" class="form-control" formControlName="tagGroupement">
        </div>
        <div class="col-12 col-sm-6">
          <label for="tagClient">Tag Client</label>
          <input type="text" name="tagClient" class="form-control" formControlName="tagClient">
        </div>
      </div>
    </div>
     
    </form>
</div>
 <div class="modal-footer justify-content-start" drawer-footer>
    <div class="col-12 d-flex flex-wrap gap-2">
      <button class="btn btn-primary" form="clientSiteFilterForm" tabindex="-1"  type="submit">Recherche</button>
      <button class="btn btn-dark"  tabindex="-1" type="button" (click)="filterCommand('reset')">Vider</button>
    </div>
  </div>
</app-custom-drawer>







<div  style="    position: fixed;
top: 0;
right: 0;
width: 100%;
height: 100%;
transition: all .3s;
z-index: 1002;
background: rgba(0, 0, 0, 0.4);" #drawerContainer [style]="isDrawerOpen ? 'visibility: visible; pointer-events: auto;opacity: 1 ' : 'visibility: hidden; pointer-events: none;opacity: 0'" (click)="toggleDrawer(false)">
<div class="bg-white" [style]="isDrawerOpen ? 'transform: translateX(0)' : 'transform: translateX(100%)'" style="width: calc(100% - 250px);height: 100%; right: 0; position: fixed;transition: all .3s; overflow-y: scroll;     scrollbar-width: thin;"  (click)="$event.stopPropagation()"  #drawerContent>
  <div class="modal-header" style="position: sticky;top: 0;z-index: 1;background: #fff;">
    <h5 class="modal-title" style="line-height: 1;">Association Client Site</h5>
    <button type="button" class="cross-button" data-bs-dismiss="modal" aria-label="Close" (click)="toggleDrawer(false)">
      <i class="mdi mdi-close"></i>
    </button>
  </div>
  <div class="modal-body" style="display: flex;
    flex-direction: column;
    height: calc(100% - 46px);">
    <ng-container [ngTemplateOutlet]="noData" *ngIf="isLoadingTransco"></ng-container>
      <kendo-grid [kendoGridBinding]="[clickedItem]" 
      [ngClass]="{'client-have-association-grid': clientHaveTransco() , 'ref-grid': !clientHaveTransco()}"
      class=" content-wrap flex-shrink-0" [height]="150"  *ngIf="!isLoadingTransco">
        <ng-template kendoGridToolbarTemplate>
          <div [ngClass]="{'client-have-association-bg':clientHaveTransco(),'client-have-no-association-bg':!clientHaveTransco()}" style="height: 44px;" class="d-flex justify-content-between align-items-center px-2">
            <span class="text-white fs-4 k-font-weight-bold">Client Site ({{clientHaveTransco() ? "Client déjà associé" :'Client non associé'}}) </span>

          <button class="btn btn-success btn-sm rounded-pill m-1" (click)="detacherClientTransco()" style="padding: 2px 6px;" *ngIf="clientHaveTransco()">
            <i class="mdi mdi-link-variant-off"></i>
            Détacher</button>
          </div>
        </ng-template>
        <kendo-grid-column field="cliCode" title="Code Client Site" [width]="90"></kendo-grid-column>
        <kendo-grid-column field="cliRaiSoc" title="Raison Sociale" [width]="200">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem?.cliRaiSoc"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="cliNomPhar" title="Nom Pharmacien" [width]="200">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem?.cliNomPhar"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="cliVille" title="Ville" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="cliAdresse" title="Adresse" [width]="200"></kendo-grid-column>
        <kendo-grid-column field="cliLocalite" title="Localité" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="telephone" title="Téléphone" [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem?.telephone"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column [width]="100" [hidden]="clientHaveTransco()"></kendo-grid-column>
      </kendo-grid>
   
<div class="d-flex justify-content-center align-items-center">

</div>
      <div class="row" style="flex:1;">
        <div class=" p-1 col-12" >
          <kendo-grid class="ref-grid content-wrap" [kendoGridBinding]="ClientGroupeAssociation" [height]="200" *ngIf="ClientGroupeAssociation.length > 0 && !isLoadingTransco">
            <ng-template kendoGridToolbarTemplate>
              <div style="background: #a83031;height: 44px;" class="d-flex justify-content-between align-items-center px-2">
                <span class="text-white fs-4 k-font-weight-bold">Client Groupe</span>
              </div>
            </ng-template>
            <kendo-grid-column field="codeClientGroupe" title="Code Client Groupe" [width]="90"></kendo-grid-column>
            <kendo-grid-column field="raisonSociale" title="Raison Sociale" [width]="200">
              <ng-template kendoGridCellTemplate let-dataItem>
                 <app-copy-cell [value]="dataItem.raisonSociale"></app-copy-cell>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="nomPharmacien" title="Nom Pharmacien" [width]="200">
              <ng-template kendoGridCellTemplate let-dataItem>
                 <app-copy-cell [value]="dataItem.nomPharmacien"></app-copy-cell>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="ville.libelle" title="Ville" [width]="100"></kendo-grid-column>
            <kendo-grid-column field="adresse1" title="Adresse" [width]="200"></kendo-grid-column>
            <kendo-grid-column field="localite" title="Localité" [width]="100"></kendo-grid-column>
            <kendo-grid-column field="telephone" title="Téléphone" [width]="100">
              <ng-template kendoGridCellTemplate let-dataItem>
                <app-copy-cell [value]="dataItem.telephone"></app-copy-cell>
              </ng-template>
            </kendo-grid-column>
          </kendo-grid>
          <ng-container [ngTemplateOutlet]="modalAttachClient" *ngIf="ClientGroupeAssociation.length === 0 && !isLoadingTransco"></ng-container>
        
        </div>

       
      </div>
    </div>
</div>
</div>





<app-custom-drawer
[width]="'70%'"
[isOpen]="isCreateClientGroupeDrawerOpen"
[title]="'Créer Client Groupe'"
(isOpenChange)="isCreateClientGroupeDrawerOpen = $event"
>
  <div drawer-body class="p-2">
  <form [formGroup]="clientGroupeForm" id="clientGroupeForm" class="container mt-2 flex-grow-1 d-flex flex-column w-100" (ngSubmit)="onSubmitCreateClientGroupe()" appFocusTrap >

     <div class="row">
      <div class="col-8">
        <div class="row">
          <div class="col-md-6 mb-1">
            <label for="raisonSociale" class="form-label">Raison Sociale<span class="text-danger">*</span></label>
            <input id="raisonSociale"  formControlName="raisonSociale" class="form-control raison-social-create" required
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('raisonSociale')?.invalid}"
            >
            <div *ngIf="clientGroupeForm.get('raisonSociale')?.invalid && formSubmited" class="text-danger">
              Raison Sociale est obligatoire.
            </div>
          </div>
      
          <div class="col-md-6 mb-1">
            <label for="nomDuPharmacien" class="form-label">Nom du Pharmacien<span class="text-danger">*</span></label>
            <input id="nomDuPharmacien" formControlName="nomDuPharmacien" class="form-control" required
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('nomDuPharmacien')?.invalid}"
            >
            <div *ngIf="clientGroupeForm.get('nomDuPharmacien')?.invalid && formSubmited" class="text-danger">
              Nom du Pharmacien est obligatoire.
            </div>
          </div>
        </div>
      
        <div class="row">
          <div class="col-md-6 mb-1">
            <label for="adresse" class="form-label">Adresse<span class="text-danger">*</span></label>
            <input id="adresse" formControlName="adresse" class="form-control" required
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('adresse')?.invalid}"
            >
            <div *ngIf="clientGroupeForm.get('adresse')?.invalid && formSubmited" class="text-danger">
              Adresse est obligatoire.
            </div>
          </div>
      
          <div class="col-md-6 mb-1">
            <label for="adresseComplement" class="form-label">Adresse (Complément)</label>
            <input id="adresseComplement" formControlName="adresseComplement" class="form-control">
          </div>
        </div>
      
        <div class="row">
          <div class="col-md-6 mb-1">
            <label for="localite" class="form-label">Localité</label>
            <input [ngbTypeahead]="searchLocaliteTypeahead" class="form-control" id="localite"
            formControlName="localite" [inputFormatter]="formatterLocalite"  type="search"
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('localite')?.invalid}"
            placeholder="taper localité"  [editable]="true"
            [resultFormatter]="formatterLocalite">
          </div>
      
          <div class="col-md-6 mb-1">
            <label for="ville" class="form-label">Ville<span class="text-danger">*</span></label>
            <input [ngbTypeahead]="searchVilleTypeahead" class="form-control" id="localite"
            formControlName="ville" [inputFormatter]="formatter" [editable]="false"  type="search"
               [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('ville')?.invalid}"
            placeholder="taper ville"
            [resultFormatter]="formatter">
            <div *ngIf="clientGroupeForm.get('ville')?.invalid && formSubmited" class="text-danger">
              Ville est obligatoire.
            </div>
          </div>
        </div>
      
        <div class="row">
          <div class="col-md-6 mb-1">
            <label for="telephone" class="form-label">Téléphone<span class="text-danger">*</span></label>
            <input id="telephone" formControlName="telephone" class="form-control" required
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('telephone')?.invalid}"
            >
            <div *ngIf="clientGroupeForm.get('telephone')?.invalid && formSubmited" class="text-danger">
              Téléphone est obligatoire.
            </div>
          </div>
          <div class="col-md-6 mb-1">
            <label for="email" class="form-label">Email</label>
            <input id="email" formControlName="email" class="form-control"  placeholder="ex: <EMAIL>"
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('email')?.invalid}"
            >
            <div *ngIf="clientGroupeForm.get('email')?.invalid && formSubmited" class="text-danger">
              Email est non valide.
            </div>
          </div>
          <div class="col-md-6 mb-1">
            <label for="telephone2" class="form-label">Téléphone 2</label>
            <input id="telephone2" formControlName="telephone2" class="form-control"  placeholder="ex: 06 00 00 00 00"
            >
          </div>
          <div class="col-md-6 mb-1">
            <label for="gsm" class="form-label">Gsm</label>
            <input id="gsm" formControlName="gsm" class="form-control" placeholder="ex: 06 00 00 00 00">
          </div>
        </div>
        <!--  -->
        <div class="row">
       
          <div class="col-md-6 mb-1">
            <label for="whatsapp" class="form-label">Whatsapp</label>
            <input id="whatsapp" formControlName="whatsapp" class="form-control"  placeholder="ex: 06 00 00 00 00">
          </div>
          <div class="col-md-6 mb-1">
            <label for="ice" class="form-label">ICE</label>
            <input id="ice" formControlName="ice" class="form-control"  placeholder="ex: 06 00 00 00 00">
          </div>
        </div>
        <!--  -->
        <div class="row">
          <div class="col-md-6 mb-1">
            <label for="inpe" class="form-label">inpe</label>
            <input id="inpe" formControlName="inpe" class="form-control"  minlength="9" maxlength="9"
            >
            
          </div>
          <div class="col-md-6 mb-1">
            <label for="patente" class="form-label">Patente</label>
            <input id="patente" formControlName="patente" class="form-control" 
            >
          </div>
        </div>
      </div>
      <div class="col-4">
           <div class="row">
            <div class="col-6">
              <label for="latitude" class="form-label">Latitude</label>
              <input id="latitude" formControlName="latitude" class="form-control">
            </div>
            <div class="col-6">
              <label for="longitude" class="form-label">Longitude</label>
              <input id="longitude" formControlName="longitude" class="form-control">
            </div>
          </div>
         <div class="row mt-2">
          <div class="map-container" *ngIf="isCreateClientGroupeDrawerOpen">
            <app-map (coords)="onMarkerSelected($event)" [selectedLocation]="{
              latitude: clientGroupeForm.get('latitude').value,
              longitude: clientGroupeForm.get('longitude').value
              }"></app-map>
          </div>
        </div>
      </div>
     </div>
</form>
</div>
<div class="modal-footer mt-auto" style="position: sticky;bottom: 0;z-index: 1;background: #fff;" drawer-footer>
  <button type="button" class="btn btn-secondary"   tabindex="-1" (click)="toggleClientGroupeDrawer(false)" >Annuler</button>
  <button type="submit" class="btn btn-primary" form="clientGroupeForm" tabindex="-1">
      Enregistrer
  </button>
</div>
</app-custom-drawer>


<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">List Client Sites</h4>
      <div class="d-flex flex-wrap justify-content-end gap-2">
        <div class="" ngbDropdown  [autoClose]="'outside'" >
          <button class="btn btn-outline-primary" ngbDropdownToggle>
            <i class="mdi mdi-table-column-plus-before" *ngIf="!isAllColumnsVisible"></i>
            <i class="mdi mdi-table-column-remove" *ngIf="!hasHiddenColumns"></i>
            Colonne visibilité
          </button>
          <div ngbDropdownMenu>
            <label class="form-check-label user-select-none colum-choose-dropdown"  for="{{column}}" ngbDropdownItem *ngFor="let column of columns" class="user-select-none">
                <input type="checkbox" class="form-check-input user-select-none" [checked]="!isColumnHidden(column)" (change)="toggleColumnVisibility(column)" id="{{column}}">
                {{getColumnTitle(column)}}
              </label>
          </div>
        </div>
        <div class="btn-group">
          <button class="btn btn-dark" (click)="openFilterDrawer()">
            <i class="mdi mdi-filter"></i>
            Filtrer
          </button>
          <ng-container *ngIf="hasActiveFilters">
            <button class="btn btn-outline-dark" title="Actualiser le filtre actuel" (click)="filterCommand('refresh')">
              <i class="mdi mdi-refresh"></i>
            </button>
            <button class="btn btn-outline-dark" (click)="filterCommand('reset')" title="Effacer le filtre actuel">
              <i class="mdi mdi-filter-remove"></i>
            </button>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>


<kendo-grid [data]="clientsSite"  
style="height: calc(100vh - 130px);border-radius: 10px;" class="winClient-stats-grid ref-grid content-wrap custom-sort-grid"
[pageable]="true"
[pageSize]="navigation.pageSize"
[skip]="navigation.skip"
[rowClass]="rowClass"
[sortable]="{mode: 'single'}"
(sortChange)="onSortChange($event)"
[sort]="clientSiteSort"
>

  <kendo-grid-column field="codeSite" title="Site" [width]="70">
    <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
      <app-grid-sort-header [title]="column.title" [type]="'numeric'" [active]="navigation.sortField === column.field" [direction]="navigation.sortMethod"></app-grid-sort-header>
    </ng-template>
    <ng-template kendoGridCellTemplate let-dataItem>
      <!-- {{dataItem.siteLabel}}({{dataItem.codeSite}}) -->
       <span [ngClass]="{'text-success font-weight-bold': dataItem?.estActifSite}">
         {{dataItem.codeSite | clientSitePipe }} 
       </span>
      </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="cliCode" title="Code local" [width]="60">
    <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
      <app-grid-sort-header [title]="column.title" [type]="'numeric'" [active]="navigation.sortField === column.field" [direction]="navigation.sortMethod"></app-grid-sort-header>
     </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="cliRaiSoc" title="Raison Sociale Locale" [width]="150">
      <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
          <app-grid-sort-header [title]="column.title"  
          [active]="navigation.sortField === column.field"
          [direction]="navigation.sortMethod"></app-grid-sort-header>
     </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="cliNomPhar" title="Nom Pharmacien" [width]="150">
       <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
          <app-grid-sort-header [title]="column.title"  
          [active]="navigation.sortField === column.field"
          [direction]="navigation.sortMethod"></app-grid-sort-header>
     </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="cliAdresse" title="Adresse" [width]="150" [hidden]="isColumnHidden('cliAdresse')"></kendo-grid-column>
  <kendo-grid-column field="cliLocalite" title="Localité" [width]="100">
    <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
      <app-grid-sort-header [title]="column.title"  
      [active]="navigation.sortField === column.field"
      [direction]="navigation.sortMethod"></app-grid-sort-header>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="cliVille" title="Ville" [width]="100" >
    <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
      <app-grid-sort-header [title]="column.title"  
      [active]="navigation.sortField === column.field"
      [direction]="navigation.sortMethod"></app-grid-sort-header>
  </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="cliCategorie" title="Catégorie" [width]="70" [hidden]="isColumnHidden('cliCategorie')"></kendo-grid-column>
  <kendo-grid-column field="cliPatente" title="Patente" [width]="100" [hidden]="isColumnHidden('cliPatente')"></kendo-grid-column>
  <kendo-grid-column field="cliTel" title="Téléphone" [width]="100"></kendo-grid-column>
  <kendo-grid-column field="dateMaj" title="Date Maj" [width]="120" [hidden]="isColumnHidden('dateMaj')">
    <ng-template kendoGridCellTemplate let-dataItem>
      {{ dataItem.dateMaj | date: 'dd/MM/yyyy' }}
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="estActifSite" title="Status" [width]="60" [hidden]="isColumnHidden('estActifSite')">
    <ng-template kendoGridCellTemplate let-dataItem>
      <span *ngIf="dataItem.estActifSite" class="badge bg-success">Actif</span>
      <span *ngIf="!dataItem.estActifSite" class="badge bg-danger">Inactif</span>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="estSignale" title="Signalé" [width]="70" [hidden]="isColumnHidden('estSignale')">
    <ng-template kendoGridCellTemplate let-dataItem>
      <span class="badge badge-danger" *ngIf="dataItem.estSignale === 'O'">
        <i class="mdi mdi-alert-circle"></i> Oui
      </span>
      <span class="badge badge-success" *ngIf="dataItem.estSignale === 'N' || !dataItem.estSignale">
        <i class="mdi mdi-check-circle"></i> Non
      </span>

    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="tag" title="Tag" [width]="100" [hidden]="isColumnHidden('tag')"></kendo-grid-column>
   <kendo-grid-column title="Actions" [width]="80" class="sicky-to-right" headerClass="sicky-to-right sicky-to-right-header">
    <ng-template kendoGridCellTemplate let-dataItem>
      <app-action-icon [icon]="'magnify'" class="test"
      (click)="openAssociationGroupeModal(dataItem)"
      [extendClass]="'circle-lg'" title="Attacher a un client groupe">

      </app-action-icon>
      <app-action-icon [icon]="'plus'" [backgroundColor]="'success'" [extendClass]="'circle-lg'" (click)="openCreateClientGroupeDrawer(dataItem)" title="Créer un client groupe">

      </app-action-icon>
      
    </ng-template>
  </kendo-grid-column>
  <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
  let-total="total">
  <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"  [allowPageSizes]="false"
    [navigation]="navigation" style="width: 100%;"
    (pageChange)="pageChange($event)"></wph-grid-custom-pager>
</ng-template>
</kendo-grid>

<ng-template #filterModal let-modal>
  <div class="card" >
    <div class="card-header bg-dark d-flex justify-content-between align-items-center">
      <h4 class="card-title m-0 text-white">Critéres de recherche (Clients Sites)</h4>
      <i class="mdi mdi-close text-white k-cursor-pointer" style="font-size: 30px; line-height: 0;" (click)="modal.close()"></i>
    </div>
    <form class="card-body p-2" [formGroup]="clientSiteFilterForm" (ngSubmit)="onFilterSubmit()" appFocusTrap>
      <div class="row">
        <div class="col-12 col-sm-6">
          <label for="codeGroupe" class="form-label">Code Groupe</label>
          <input id="codeGroupe" formControlName="codeGroupe" class="form-control">
        </div>
        <div class="col-12 col-sm-6">
          <label for="codeLocal" class="form-label">Code Local</label>
          <input id="codeLocal" formControlName="codeLocal" class="form-control">
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-sm-6">
          <label for="raisonSociale" class="form-label">Raison Social</label>
          <input id="raisonSociale" formControlName="raisonSociale" class="form-control">
        </div>
        <div class="col-12 col-sm-6">
          <label for="nomPharmacien" class="form-label">Nom Pharmacien</label>
          <input id="nomPharmacien" formControlName="nomPharmacien" class="form-control">
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-sm-6">
          <label for="sites" class="form-label">Site</label>
          <select id="sites" formControlName="site" class="form-select">
            <option [ngValue]="null">Select Site</option>
            <option *ngFor="let site of sites" [ngValue]="site">{{ site.libelleLong}}({{site.libelleCourt}})</option>
            </select>
        </div>
        <div class="col-12 col-sm-6">
          <label for="ville" class="form-label">Ville</label>
          <select id="ville" formControlName="ville" class="form-select">
            <option [ngValue]="null">Select Ville</option>
            <option *ngFor="let ville of villes" [ngValue]="ville">{{ ville.libelle }}</option>
          </select>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-12 col-sm-6 d-flex flex-column flex-sm-row align-items-start align-items-sm-center mb-2 mb-sm-0">
          <label for="sites" class="form-label me-2 mb-1 mb-sm-0">Groupemets</label>
          <div class="w-100">
                  <app-switch
          [elements]='[{ label: "Tous", value:null},{ label: "Groupé", value:true},{ label: "Non Groupé", value:false}]'
          formControlName="isGrouped" name="groupement" [disabled]="false"
          [switchClass]="'info'"></app-switch>
          </div>
    
        </div>
        <div class="col-12 col-sm-6 d-flex flex-column flex-sm-row align-items-start align-items-sm-center">
          <label for="signalement" class="form-label me-2 mb-1 mb-sm-0">Signalement</label>
          <div class="w-100">
                  <app-switch
          [elements]='[{ label: "Tous", value:null},{ label: "Signale", value:"O"},{ label: "Non Signale", value:"N"}]'
          formControlName="signalement" name="signalement" [disabled]="false"
          [switchClass]="'info'"></app-switch>
          </div>
    
        </div>
      
      </div>
      <div class="row">
        <div class="col-12 col-sm-4 mb-2 mb-sm-0">
          <label for="nouveauClient" class="me-1">Nouveau Client ?</label>
          <input type="checkbox" class="form-check-input" formControlName="nouveauClient">
        </div>
        <div class="col-12 col-sm-4 mb-2 mb-sm-0">
          <label for="dateDebut" class="form-label">Date Debut</label>
          <app-date-picker formControlName="dateDebut"></app-date-picker>
        </div>
        <div class="col-12 col-sm-4">
          <label for="dateFin" class="form-label">Date Fin</label>
          <app-date-picker formControlName="dateFin"></app-date-picker>
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-sm-6 mb-2 mb-sm-0">
          <label for="tagGroupement">Tag Groupement</label>
          <input type="text" name="tagGroupement" class="form-control" formControlName="tagGroupement">
        </div>
        <div class="col-12 col-sm-6">
          <label for="tagClient">Tag Client</label>
          <input type="text" name="tagClient" class="form-control" formControlName="tagClient">
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-12 d-flex flex-wrap gap-2 justify-content-start">
          <button class="btn btn-primary" type="submit">Recherche</button>
          <button class="btn btn-dark" type="button" (click)="onFilterReset()">Vider</button>
        </div>
      </div>
    </form>
    </div>
</ng-template>



<ng-template #modalClientSiteRefGroupe let-modal>
  <div class="modal-header">
    <h4 class="modal-title">
      Client Association Dans Groupe
     </h4>
    <button type="button" class="cross-button" (click)="modal.close()" >
      <i class="mdi mdi-close"></i>
    </button>
  </div>
  <div class="modal-body">
      <div class="card card-body shadow-lg border-1" *ngIf="!isLoadingTransco">
        <div class="row">
          <div class="col-md-4">
            <span class="k-font-weight-bold">Code Client Site :</span> {{clickedItem?.cliCode}}
          </div>
          <div class="col-md-4">
            <span class="k-font-weight-bold">Nom du Pharmacien :</span> {{clickedItem?.cliNomPhar}}
          </div>
          <div class="col-md-4">
            <span class="k-font-weight-bold">Raison Sociale :</span> {{clickedItem?.cliRaiSoc}}
          </div>
          </div>
          <div class="row mt-1">
            <div class="col-md-4">
              <span class="k-font-weight-bold">Ville :</span> {{clickedItem?.cliVille}}
            </div>
            <div class="col-md-4">
              <span class="k-font-weight-bold">Localité :</span> {{clickedItem?.cliLocalite}}
            </div>
            <div class="col-md-4">
              <span class="k-font-weight-bold">Adresse :</span> {{clickedItem?.cliAdresse}}
            </div>
          </div>
    </div>
    <div class="card card-body d-flex flex-row k-gap-2 p-0 justify-content-end">

      <ng-container *ngIf="!isLoadingTransco">
        <!-- <button class="btn btn-primary" (click)="attacherGroupeClient()" *ngIf="ClientGroupeAssociation.length === 0 ">Attacher</button> -->
         
        <app-popover-confirm [title]="'Confirmation'" 
        [message]="'Voulez-vous vraiment attacher ce client à un groupe ?'" 
       
        >
                <button class="btn btn-primary" *ngIf="ClientGroupeAssociation.length > 0">Détacher</button>
        </app-popover-confirm>
      <!-- <button class="btn btn-dark" (click)="attacherGroupeClient('update')" *ngIf="ClientGroupeAssociation.length > 0"> -->
       <!-- Ré-attacher
      </button> -->
      </ng-container>
    </div>
    <kendo-grid class="ref-grid content-wrap" [kendoGridBinding]="ClientGroupeAssociation"  *ngIf="ClientGroupeAssociation.length > 0  && !isLoadingTransco">
      <kendo-grid-column field="codeClientGroupe" title="Code Client Groupe" [width]="100"></kendo-grid-column>
      <kendo-grid-column field="nomPharmacien" title="Nom Pharmacien" [width]="200"></kendo-grid-column>
      <kendo-grid-column field="raisonSociale" title="Raison Sociale" [width]="200"></kendo-grid-column>
      <kendo-grid-column field="adresse1" title="Adresse" [width]="200"></kendo-grid-column>
      <kendo-grid-column field="localite" title="Localité" [width]="100"></kendo-grid-column>
      <kendo-grid-column field="ville.libelle" title="Ville" [width]="100"></kendo-grid-column>
      <kendo-grid-column field="telephone" title="Téléphone" [width]="100"></kendo-grid-column>
    </kendo-grid>
    <ng-container [ngTemplateOutlet]="modalAttachClient" *ngIf="ClientGroupeAssociation.length === 0 && !isLoadingTransco"></ng-container>
    <ng-container [ngTemplateOutlet]="noData" *ngIf="isLoadingTransco"></ng-container>
    
  </div>
</ng-template>
<ng-template  kendoGridNoRecordsTemplate #noData>
  <!--  loading spinner -->
 
  <div class="d-flex align-items-center justify-content-cenrter flex-column">
    <div class="d-flex align-items-center justify-content-center flex-column p-3" *ngIf="isLoadingTransco">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">chargement...</span>
    </div>
    </div>
  <p  class="font-weight-600 fs-3 mt-2">En cours de Recherche pour Client Groupe...</p>
  </div>
  
</ng-template>

<ng-template #modalAttachClient>
<div class="card" style="border-radius:0 0 20px 20px ; overflow: hidden; border: 1px solid #ededed !important;">
           <div class="row">
            <div class="col-12">
              <div class="card">
                <div class="card-header border px-2 py-1">
                  <h5 class="card-title m-0">Filtrer Client Groupe</h5>
                </div>
                <div class="card-body p-1 border">
                  <form [formGroup]="clientGroupeFilterForm" appFocusTrap>
                    <div class="row align-items-end">
                      <div class="col">
                        <label for="codeGroupe" class="form-label mb-0">Code Groupe</label>
                        <input id="codeGroupe" formControlName="codeClientGroupe" class="form-control">
                      </div>
                      <div class="col">
                        <label for="raisonSociale" class="form-label mb-0">Raison Sociale</label>
                        <input id="raisonSociale" formControlName="raisonSociale" class="form-control">
                      </div>
                      <div class="col">
                        <label for="nomPharmacien" class="form-label mb-0">Nom Pharmacien</label>
                        <input id="nomPharmacien" formControlName="nomPharmacien" class="form-control">
                      </div>
                      <div class="col">
                        <label for="ville" class="form-label mb-0">Ville</label>
                        <select id="ville" formControlName="ville" class="form-select">
                          <option [ngValue]="null">Select Ville</option>
                          <option *ngFor="let ville of villes" [ngValue]="ville">{{ ville.libelle }}</option>
                        </select>
                      </div>
                      <div class="col">
                        <label for="adresse" class="form-label mb-0">Adresse</label>
                        <input id="adresse" formControlName="adresse" class="form-control">
                      </div>
                      <div class="col">
                        <div class="d-flex gap-2">
                          <button class="btn btn-primary" tabindex="-1" (click)="filterClientGroupe()">Recherche</button>
                          <button class="btn btn-warning" tabindex="-1" (click)="searchClientGroupeByTac()">
                            <i class="mdi mdi-lightbulb-on-outline"></i>
                          </button>
                          <button class="btn btn-dark" tabindex="-1" (click)="clientGroupeFilterForm.reset()">Vider</button>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
  <div>
    <div class="card-body p-0">
      <div class="row">
        <div class="col-12">
        <kendo-grid class="ref-grid content-wrap" [data]="clientsGroupe"  [pageable]="true" 
          [pageSize]="clientGroupeNavigation.pageSize" [skip]="clientGroupeNavigation.skip"
          [height]="593">
          <kendo-grid-column field="codeClientGroupe" title="Code Client Groupe" [width]="100"></kendo-grid-column>
          <kendo-grid-column field="raisonSociale" title="Raison Sociale" [width]="200"></kendo-grid-column>
          <kendo-grid-column field="nomPharmacien" title="Nom Pharmacien" [width]="200"></kendo-grid-column>
          <kendo-grid-column field="ville.libelle" title="Ville" [width]="100"></kendo-grid-column>
          <kendo-grid-column field="adresse1" title="Adresse" [width]="200"></kendo-grid-column>
          <kendo-grid-column field="localite" title="Localité" [width]="100"></kendo-grid-column>
          <kendo-grid-column field="telephone" title="Téléphone" [width]="100"></kendo-grid-column>
          <kendo-grid-column title="Action" [width]="100" class="sicky-to-right" headerClass="sicky-to-right sicky-to-right-header">
            <ng-template kendoGridCellTemplate let-dataItem>
              <button class="btn btn-success px-1 btn-sm flex-shrink-0 rounded-pill" (click)="attacherGroupeClient(dataItem)">
                <i class="mdi mdi-link-variant"></i>Attacher
              </button>
            </ng-template>
          </kendo-grid-column>
          <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
            let-total="total">
            <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage" 
              [allowPageSizes]="false" [navigation]="clientGroupeNavigation" style="width: 100%;"
              (pageChange)="clientGroupePageChange($event)"></wph-grid-custom-pager>
          </ng-template>
        </kendo-grid>
        </div>
     
      </div>
      
    </div>
  </div>
</div>
</ng-template>
