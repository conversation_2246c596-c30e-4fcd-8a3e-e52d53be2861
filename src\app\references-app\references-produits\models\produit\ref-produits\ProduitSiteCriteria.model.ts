export interface ProduitSiteCriteria {
    codeBarre?: string;
    codeGroupe?: string;
    codeProSite?: string;
    codeSite?: string[];
    designation?: string;
    labelleLabo?: string;
    produitSupp?: boolean;
    produitTransco?: boolean; 
  }




export interface IProduitSiteCriteriaForm {
  codeBarre: string;
  codeGroupe: string;
  codeProSite: string;
  codeSite: number[];
  codeWinplus: string;
  dateCreateDu: string;
  dateCreateAu: string;
  dateDFPAu: string;
  dateDFPDu: string;
  dateEnvoieAu: string;
  dateEnvoieDu: string;
  designation: string;
  envoyer: boolean;
  labelleLabo: string;
  produitSupp: boolean;
  transcoCodeGroupe: boolean;
  transcoWinplus: boolean;
  typeFrn: string;
  codeSophatel: string;
  estActif: boolean;
  }


export class ProduitSiteCriteriaForm implements IProduitSiteCriteriaForm {
    codeBarre: string;
    codeGroupe: string;
    codeProSite: string;
    codeSite: number[];
    codeWinplus: string;
    dateCreateDu: string;
    dateCreateAu: string;
    dateDFPAu: string;
    dateDFPDu: string;
    dateEnvoieAu: string;
    dateEnvoieDu: string;
    designation: string;
    envoyer: boolean;
    labelleLabo: string;
    produitSupp: boolean;
    transcoCodeGroupe: boolean;
    transcoWinplus: boolean;
    typeFrn: string;
    codeSophatel: string;
    estActif: boolean;

    constructor(data: Partial<IProduitSiteCriteriaForm>) {
      Object.assign(this, data);
    }
  }