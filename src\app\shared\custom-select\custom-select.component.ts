import { 
  Component, 
  ElementRef, 
  EventEmitter, 
  HostListener, 
  Input, 
  OnDestroy, 
  OnInit, 
  Output, 
  ViewChild,
  ChangeDetectorRef,
  NgZone
  } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { 
  autoUpdate, 
  computePosition, 
  flip, 
  offset, 
  shift, 
  size
} from '@floating-ui/dom';

export interface SelectOption {
  value: any;
  label: string;
  disabled?: boolean;
}

@Component({
  selector: 'app-custom-select',
  template: `
    <div class="custom-select-container" [class.disabled]="disabled">
      <label *ngIf="label" [attr.for]="id" class="select-label">{{ label }}</label>
      
      <div class="select-wrapper" 
           [attr.aria-disabled]="disabled" 
           [class.focused]="isFocused"
           [class.error]="showError">
        
        <button
          #selectTrigger
          [id]="id"
          type="button"
          class="select-trigger"
          [class.open]="isOpen"
          [class.multi-select]="multiSelect"
          [disabled]="disabled"
          [attr.aria-haspopup]="'listbox'"
          [attr.aria-expanded]="isOpen"
          [attr.aria-labelledby]="ariaLabelledBy || null"
          [attr.aria-label]="ariaLabel || null"
          [attr.aria-describedby]="ariaDescribedBy || null"
          [attr.aria-required]="required"
          [attr.aria-controls]="isOpen ? listboxId : null"
          (click)="toggleDropdown()"
          (focus)="onFocus()"
          (blur)="onBlur()">
          
          <div class="select-value">
            <ng-container *ngIf="!multiSelect && selectedOption; else multiSelectOrPlaceholder">
              {{ selectedOption.label }}
            </ng-container>
            <ng-template #multiSelectOrPlaceholder>
              <ng-container *ngIf="multiSelect && selectedOptions.length > 0; else placeholderTemplate">
                <div class="multi-select-tags">
                  <span 
                    *ngFor="let option of selectedOptions.slice(0, maxTagsDisplay); trackBy: trackByValue"
                    class="tag">
                    {{ option.label }}
                    <button
                      type="button"
                      class="tag-remove"
                      [attr.aria-label]="'Remove ' + option.label"
                      (click)="removeOption(option, $event)"
                      (keydown.enter)="removeOption(option, $event)"
                      (keydown.space)="removeOption(option, $event)">
                      ×
                    </button>
                  </span>
                  <span 
                    *ngIf="selectedOptions.length > maxTagsDisplay" 
                    class="tag-overflow">
                    +{{ selectedOptions.length - maxTagsDisplay }} plus
                  </span>
                </div>
              </ng-container>
              <ng-template #placeholderTemplate>
                <span >{{ placeholder }}</span>
              </ng-template>
            </ng-template>
          </div>
          
          <div class="select-actions">
            <button
              *ngIf="showClearButton && hasValue()"
              type="button"
              class="clear-button"
              [attr.aria-label]="'Clear selection'"
              (click)="clearSelection($event)"
              (keydown.enter)="clearSelection($event)"
              (keydown.space)="clearSelection($event)">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
            
            <span class="select-arrow" aria-hidden="true">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </span>
          </div>
        </button>
        
        <div
          #dropdown
          class="select-dropdown"
          [id]="listboxId"
          role="listbox"
          [attr.aria-labelledby]="id"
          [attr.aria-multiselectable]="multiSelect"
          [class.visible]="isOpen"
          (mousedown)="$event.preventDefault()">
          
          <div class="search-wrapper">
            <input
              #searchInput
              type="text"
              class="search-input"
              placeholder="Search..."
              [(ngModel)]="searchText"
              (input)="onSearch()"
              (keydown)="onSearchKeydown($event)"
              (focus)="onFocus()"
              (blur)="onBlur()"
              aria-label="Search options"
              [attr.aria-controls]="listboxId" />
          </div>
          
          <div class="options-list">
            <div
              *ngIf="filteredOptions.length === 0"
              class="no-results">
              No options found
            </div>
            
            <div
              *ngFor="let option of filteredOptions; let i = index"
              [id]="listboxId + '-' + i"
              class="select-option"
              role="option"
              [class.disabled]="option.disabled"
              [class.selected]="isOptionSelected(option)"
              [class.focused]="focusedOptionIndex === i"
              [attr.aria-selected]="isOptionSelected(option)"
              [attr.aria-disabled]="option.disabled"
              [tabindex]="option.disabled ? -1 : 0"
              (click)="!option.disabled && selectOption(option)"
              (keydown.enter)="!option.disabled && selectOption(option)"
              (keydown.space)="!option.disabled && selectOption(option); $event.preventDefault()">
              
              <div class="option-content">
                <span 
                  *ngIf="multiSelect" 
                  class="checkbox"
                  [class.checked]="isOptionSelected(option)"
                  aria-hidden="true">
                  <svg *ngIf="isOptionSelected(option)" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </span>
                <span class="option-label">{{ option.label }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div *ngIf="errorMessage && showError" class="error-message" [attr.id]="errorId">
        {{ errorMessage }}
      </div>
    </div>
  `,
  styles: [`
    .custom-select-container {
      position: relative;
      width: 100%;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }
    
    .custom-select-container.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    
    .select-label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #333;
    }
    
    .select-wrapper {
      position: relative;
      width: 100%;
    }
    
    .select-wrapper.error {
      border-color: #dc3545;
    }
    
    .select-trigger {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      min-height: 2.5rem;
      padding: 0.5rem 1rem;
      font-size: 1rem;
      background-color: #fff;
      border: 1px solid #d1d5db;
      border-radius: 0.375rem;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: left;
    }

    .select-trigger.multi-select {
      min-height: 2.75rem;
      padding: 0.375rem 1rem;
    }
    
    .select-wrapper.focused .select-trigger {
      border-color: #4f46e5;
      box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
      outline: none;
    }
    
    .select-wrapper.error .select-trigger {
      border-color: #dc3545;
    }
    
    .select-trigger:hover:not(:disabled) {
      border-color: #9ca3af;
    }
    
    .select-trigger:disabled {
      background-color: #f3f4f6;
      cursor: not-allowed;
    }
    
    .select-value {
      flex: 1;
      overflow: hidden;
      display: flex;
      align-items: center;
      min-height: 1.5rem;
    }

    .multi-select-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 0.25rem;
      width: 100%;
    }

    .tag {
      display: inline-flex;
      align-items: center;
      background-color: #f3f4f6;
      border: 1px solid #d1d5db;
      border-radius: 0.25rem;
      padding: 0.125rem 0.25rem 0.125rem 0.5rem;
      font-size: 0.875rem;
      max-width: 150px;
      overflow: hidden;
    }

    .tag-remove {
      margin-left: 0.25rem;
      padding: 0;
      background: none;
      border: none;
      cursor: pointer;
      color: #6b7280;
      font-size: 1rem;
      line-height: 1;
      border-radius: 0.125rem;
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: color 0.2s;
    }

    .tag-remove:hover {
      color: #dc3545;
      background-color: #fee2e2;
    }

    .tag-overflow {
      display: inline-flex;
      align-items: center;
      color: #6b7280;
      font-size: 0.875rem;
      padding: 0.125rem 0.5rem;
    }
    
    .placeholder {
      color: #9ca3af;
    }

    .select-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-left: 0.5rem;
    }

    .clear-button {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0.25rem;
      background: none;
      border: none;
      cursor: pointer;
      color: #6b7280;
      border-radius: 0.25rem;
      transition: all 0.2s ease;
    }

    .clear-button:hover {
      color: #dc3545;
      background-color: #fee2e2;
    }
    
    .select-arrow {
      display: flex;
      transition: transform 0.2s ease;
    }
    
    .select-trigger.open .select-arrow {
      transform: rotate(180deg);
    }
    
    .select-dropdown {
      position: absolute;
      z-index: 1000;
      width: 100%;
      max-height: 20rem;
      margin-top: 0.25rem;
      background-color: #fff;
      border: 1px solid #d1d5db;
      border-radius: 0.375rem;
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      overflow: hidden;
      opacity: 0;
      transform: translateY(-10px);
      // transition: opacity 0.2s ease, transform 0.2s ease;
      visibility: hidden;
    }
    
    .select-dropdown.visible {
      opacity: 1;
      transform: translateY(0);
      visibility: visible;
    }
    
    .search-wrapper {
      padding: 0.75rem;
      border-bottom: 1px solid #e5e7eb;
    }
    
    .search-input {
      width: 100%;
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
      border: 1px solid #d1d5db;
      border-radius: 0.25rem;
    }
    
    .search-input:focus {
      outline: none;
      border-color: #4f46e5;
      box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
    }
    
    .options-list {
      max-height: 15rem;
      overflow-y: auto;
      scrollbar-width: thin;
    }
    
    .select-option {
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .option-content {
      display: flex;
      align-items: center;
      padding: 0.75rem 1rem;
      gap: 0.75rem;
    }

    .checkbox {
      width: 16px;
      height: 16px;
      border: 1px solid #d1d5db;
      border-radius: 0.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      transition: all 0.2s;
    }

    .checkbox.checked {
      background-color: #4f46e5;
      border-color: #4f46e5;
      color: white;
    }

    .option-label {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .select-option:not(.disabled):hover {
      background-color: #f3f4f6;
    }
    
    .select-option.selected:not(.disabled) {
      background-color: #ebe9fe;
      color: #4f46e5;
      font-weight: 500;
    }
    
    .select-option.focused {
      background-color: #f3f4f6;
    }
    
    .select-option.disabled {
      color: #9ca3af;
      cursor: not-allowed;
    }

    .select-option.disabled .checkbox {
      opacity: 0.5;
    }
    
    .no-results {
      padding: 0.75rem 1rem;
      color: #6b7280;
      text-align: center;
    }
    
    .error-message {
      margin-top: 0.375rem;
      color: #dc3545;
      font-size: 0.875rem;
    }
  `]
})
export class CustomSelectComponent implements OnInit, OnDestroy {
  @Input() id = `select-${Math.random().toString(36).substring(2, 9)}`;
  @Input() label = '';
  @Input() placeholder = 'Select an option';
  @Input() options: SelectOption[] = [];
  @Input() value: any = null;
  @Input() disabled = false;
  @Input() required = false;
  @Input() errorMessage = '';
  @Input() showError = false;
  @Input() ariaLabel?: string;
  @Input() ariaLabelledBy?: string;
  @Input() ariaDescribedBy?: string;
  
  // New inputs for multi-select and clear button
  @Input() multiSelect = false;
  @Input() showClearButton = false;
  @Input() maxTagsDisplay = 3;

  @Output() valueChange = new EventEmitter<any>();
  @Output() openChange = new EventEmitter<boolean>();
  @Output() blurEvent = new EventEmitter<void>();
  @Output() clearEvent = new EventEmitter<void>();

  @ViewChild('selectTrigger') selectTriggerRef!: ElementRef<HTMLButtonElement>;
  @ViewChild('dropdown') dropdownRef!: ElementRef<HTMLDivElement>;
  @ViewChild('searchInput') searchInputRef!: ElementRef<HTMLInputElement>;

  listboxId = `${this.id}-listbox`;
  errorId = `${this.id}-error`;
  searchText = '';
  isOpen = false;
  isFocused = false;
  selectedOption: SelectOption | null = null;
  selectedOptions: SelectOption[] = [];
  filteredOptions: SelectOption[] = [];
  focusedOptionIndex = -1;
  cleanupPositioning: (() => void) | null = null;
  private clickOutsideListener?: (event: Event) => void;
  private focusOutTimer?: number;

  constructor(
    private elementRef: ElementRef,
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone
  ) {}

  ngOnInit(): void {
    this.filteredOptions = [...this.options];
    this.updateSelectedOptions();
    this.listboxId = `${this.id}-listbox`;
    this.errorId = `${this.id}-error`;
  }

  ngOnDestroy(): void {
    this.cleanupPositioning?.();
    this.removeClickOutsideListener();
    
    if (this.focusOutTimer) {
      clearTimeout(this.focusOutTimer);
    }
    
    if (this.isOpen) {
      this.restoreBodyScroll();
    }
  }



  @HostListener('document:mousedown', ['$event'])
  onMouseDownOutside(event: MouseEvent): void {
    console.log('Document mousedown detected', event.target, 'isOpen:', this.isOpen);
    
    if (!event.target || !this.isOpen) return;
    
    const target = event.target as Element;
    const clickedInside = this.elementRef.nativeElement.contains(target);
    
    console.log('MouseDown inside:', clickedInside, 'Target:', target);
    
    if (!clickedInside) {
      console.log('Closing dropdown due to outside mousedown');
      this.ngZone.run(() => {
        this.closeDropdown();
        this.cdr.detectChanges();
      });
    }
  }

  toggleDropdown(): void {
    if (this.disabled) return;
    this.isOpen ? this.closeDropdown() : this.openDropdown();
  }

  private originalBodyOverflow = '';
  private originalBodyPaddingRight = '';
  
  openDropdown(): void {
    if (this.disabled || this.isOpen) return;
    
    this.isOpen = true;
    this.searchText = '';
    this.filteredOptions = [...this.options];
    this.focusedOptionIndex = this.getSelectedOptionIndex();
    
    this.preventBodyScroll();
    
    // Add manual click outside listener
    this.addClickOutsideListener();
    
    setTimeout(() => {
      if (this.dropdownRef && this.selectTriggerRef) {
        const dropdown = this.dropdownRef.nativeElement;
        dropdown.style.transition = 'none';
        
        this.setupPositioning();
        
        setTimeout(() => {
          dropdown.style.removeProperty('transition');
          this.openChange.emit(true);
          this.focusSearchInput();
        }, 20);
      }
    });
  }

  closeDropdown(): void {
    if (!this.isOpen) return;
    this.isOpen = false;
    this.openChange.emit(false);
    this.cleanupPositioning?.();
    this.cleanupPositioning = null;
    this.focusedOptionIndex = -1;
    
    // Remove manual click outside listener
    this.removeClickOutsideListener();
    
    this.restoreBodyScroll();
  }

  private addClickOutsideListener(): void {
    // Use capture phase to catch events before they might be stopped
    this.clickOutsideListener = (event: Event) => {
      console.log('Manual click listener triggered', event.target, 'isOpen:', this.isOpen);
      
      if (!event.target || !this.isOpen) return;
      
      const target = event.target as Element;
      const clickedInside = this.elementRef.nativeElement.contains(target);
      
      console.log('Manual listener - Clicked inside:', clickedInside, 'Target:', target);
      
      if (!clickedInside) {
        console.log('Manual listener - Closing dropdown');
        // Use NgZone to ensure change detection works in production
        this.ngZone.run(() => {
          this.closeDropdown();
          this.cdr.detectChanges();
        });
      }
    };
    
    // Add multiple event types with different phases for maximum compatibility
    this.ngZone.runOutsideAngular(() => {
      document.addEventListener('click', this.clickOutsideListener, true);
      document.addEventListener('mousedown', this.clickOutsideListener, true);
      document.addEventListener('touchstart', this.clickOutsideListener, true);
      // Also add without capture phase as fallback
      document.addEventListener('click', this.clickOutsideListener, false);
    });
  }

  private removeClickOutsideListener(): void {
    if (this.clickOutsideListener) {
      // Remove all the event listeners we added
      document.removeEventListener('click', this.clickOutsideListener, true);
      document.removeEventListener('mousedown', this.clickOutsideListener, true);
      document.removeEventListener('touchstart', this.clickOutsideListener, true);
      document.removeEventListener('click', this.clickOutsideListener, false);
      this.clickOutsideListener = undefined;
    }
  }

  private preventBodyScroll(): void {
    if (typeof document === 'undefined') return;
    
    const body = document.body;
    const hasVerticalScrollbar = window.innerWidth > document.documentElement.clientWidth;
    
    this.originalBodyOverflow = body.style.overflow;
    this.originalBodyPaddingRight = body.style.paddingRight;
    
    if (hasVerticalScrollbar) {
      const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
      body.style.paddingRight = `${scrollbarWidth}px`;
    }
    
    body.style.overflow = 'hidden';
  }
  
  private restoreBodyScroll(): void {
    if (typeof document === 'undefined') return;
    
    const body = document.body;
    body.style.overflow = this.originalBodyOverflow;
    body.style.paddingRight = this.originalBodyPaddingRight;
  }

  setupPositioning(): void {
    const trigger = this.selectTriggerRef.nativeElement;
    const dropdown = this.dropdownRef.nativeElement;
    
    computePosition(trigger, dropdown, {
      placement: 'bottom-start',
      middleware: [
        offset(4),
        flip(),
        shift({ padding: 8 }),
        size({
          apply({ availableWidth, availableHeight, elements }) {
            Object.assign(elements.floating.style, {
              maxWidth: `${availableWidth}px`,
              maxHeight: `${Math.min(availableHeight - 10, 300)}px`
            });
          },
        }),
      ],
    }).then(({ x, y }) => {
      Object.assign(dropdown.style, {
        left: `${x}px`,
        top: `${y}px`,
        width: `${trigger.offsetWidth}px`,
      });
      
      this.cleanupPositioning = autoUpdate(trigger, dropdown, () => {
        computePosition(trigger, dropdown, {
          placement: 'bottom-start',
          middleware: [
            offset(4),
            flip(),
            shift({ padding: 8 }),
            size({
              apply({ availableWidth, availableHeight, elements }) {
                Object.assign(elements.floating.style, {
                  maxWidth: `${availableWidth}px`,
                  maxHeight: `${Math.min(availableHeight - 10, 300)}px`
                });
              },
            }),
          ],
        }).then(({ x, y }) => {
          Object.assign(dropdown.style, {
            left: `${x}px`,
            top: `${y}px`,
            width: `${trigger.offsetWidth}px`,
          });
        });
      });
    });
  }

  onSearch(): void {
    const searchTerm = this.searchText.toLowerCase().trim();
    
    if (!searchTerm) {
      this.filteredOptions = [...this.options];
    } else {
      this.filteredOptions = this.options.filter(option => 
        option.label.toLowerCase().includes(searchTerm)
      );
    }
    
    this.focusedOptionIndex = this.filteredOptions.length > 0 ? 0 : -1;
  }

  onSearchKeydown(event: KeyboardEvent): void {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        this.focusNextOption();
        break;
      case 'ArrowUp':
        event.preventDefault();
        this.focusPreviousOption();
        break;
      case 'Enter':
        event.preventDefault();
        if (this.focusedOptionIndex >= 0) {
          this.selectOption(this.filteredOptions[this.focusedOptionIndex]);
        }
        break;
      case 'Escape':
        event.preventDefault();
        this.closeDropdown();
        this.selectTriggerRef.nativeElement.focus();
        event.stopPropagation();
        break;
      case 'Tab':
        this.closeDropdown();
        break;
    }
  }

  @HostListener('keydown', ['$event'])  
  onKeyDown(event: KeyboardEvent): void {
    if (!this.isOpen && (event.key === 'ArrowDown' || event.key === 'ArrowUp' || event.key === ' ')) {
      event.preventDefault();
      this.openDropdown();
    }
  }

  focusNextOption(): void {
    if (this.filteredOptions.length === 0) return;
    
    let nextIndex = this.focusedOptionIndex + 1;
    if (nextIndex >= this.filteredOptions.length) {
      nextIndex = 0;
    }
    
    while (nextIndex !== this.focusedOptionIndex && this.filteredOptions[nextIndex]?.disabled) {
      nextIndex = (nextIndex + 1) % this.filteredOptions.length;
      if (nextIndex === this.focusedOptionIndex) break;
    }
    
    this.focusedOptionIndex = nextIndex;
    this.scrollOptionIntoView();
  }

  focusPreviousOption(): void {
    if (this.filteredOptions.length === 0) return;
    
    let prevIndex = this.focusedOptionIndex - 1;
    if (prevIndex < 0) {
      prevIndex = this.filteredOptions.length - 1;
    }
    
    while (prevIndex !== this.focusedOptionIndex && this.filteredOptions[prevIndex]?.disabled) {
      prevIndex = (prevIndex - 1 + this.filteredOptions.length) % this.filteredOptions.length;
      if (prevIndex === this.focusedOptionIndex) break;
    }
    
    this.focusedOptionIndex = prevIndex;
    this.scrollOptionIntoView();
  }

  scrollOptionIntoView(): void {
    setTimeout(() => {
      const optionsContainer = this.dropdownRef.nativeElement.querySelector('.options-list');
      const focusedOption = this.dropdownRef.nativeElement.querySelector(`#${this.listboxId}-${this.focusedOptionIndex}`);
      
      if (optionsContainer && focusedOption) {
        const containerRect = optionsContainer.getBoundingClientRect();
        const optionRect = focusedOption.getBoundingClientRect();
        
        if (optionRect.bottom > containerRect.bottom) {
          optionsContainer.scrollTop += optionRect.bottom - containerRect.bottom;
        } else if (optionRect.top < containerRect.top) {
          optionsContainer.scrollTop -= containerRect.top - optionRect.top;
        }
      }
    });
  }

  selectOption(option: SelectOption): void {
    if (option.disabled) return;
    
    if (this.multiSelect) {
      const currentValues = Array.isArray(this.value) ? [...this.value] : [];
      const optionIndex = currentValues.findIndex(val => val === option.value);
      
      if (optionIndex >= 0) {
        // Remove option
        currentValues.splice(optionIndex, 1);
      } else {
        // Add option
        currentValues.push(option.value);
      }
      
      this.value = currentValues;
      this.updateSelectedOptions();
      this.valueChange.emit(this.value);
      
      // Keep dropdown open for multi-select
    } else {
      this.value = option.value;
      this.updateSelectedOptions();
      this.valueChange.emit(this.value);
      this.closeDropdown();
      this.selectTriggerRef.nativeElement.focus();
    }
  }

  removeOption(option: SelectOption, event: Event): void {
    event.stopPropagation();
    event.preventDefault();
    
    if (!this.multiSelect || this.disabled) return;
    
    const currentValues = Array.isArray(this.value) ? [...this.value] : [];
    const optionIndex = currentValues.findIndex(val => val === option.value);
    
    if (optionIndex >= 0) {
      currentValues.splice(optionIndex, 1);
      this.value = currentValues;
      this.updateSelectedOptions();
      this.valueChange.emit(this.value);
    }
  }

  clearSelection(event: Event): void {
    event.stopPropagation();
    event.preventDefault();
    
    if (this.disabled) return;
    
    this.value = this.multiSelect ? [] : null;
    this.updateSelectedOptions();
    this.valueChange.emit(this.value);
    this.clearEvent.emit();
  }
  clearState(): void {
    if (this.disabled) return;
    this.value = this.multiSelect ? [] : null;
    this.updateSelectedOptions();
    this.clearEvent.emit();
  }

  celarValue(): void {
    if (this.disabled) return;
    this.value = this.multiSelect ? [] : null;
    this.updateSelectedOptions();
    this.valueChange.emit(this.value);
    this.clearEvent.emit();
  }

  hasValue(): boolean {
    if (this.multiSelect) {
      return Array.isArray(this.value) && this.value.length > 0;
    }
    return this.value !== null && this.value !== undefined && this.value !== '';
  }

  updateSelectedOptions(): void {
    if (this.multiSelect) {
      const values = Array.isArray(this.value) ? this.value : [];
      this.selectedOptions = this.options.filter(option => 
        values.includes(option.value)
      );
      this.selectedOption = null;
    } else {
      this.selectedOption = this.options.find(option => option.value === this.value) || null;
      this.selectedOptions = [];
    }
  }

  isOptionSelected(option: SelectOption): boolean {
    if (this.multiSelect) {
      const values = Array.isArray(this.value) ? this.value : [];
      return values.includes(option.value);
    }
    return this.value === option.value;
  }

  getSelectedOptionIndex(): number {
    if (this.multiSelect) {
      return this.selectedOptions.length > 0 
        ? this.filteredOptions.findIndex(option => option.value === this.selectedOptions[0].value)
        : -1;
    }
    if (!this.selectedOption) return -1;
    return this.filteredOptions.findIndex(option => option.value === this.selectedOption?.value);
  }

  trackByValue(index: number, option: SelectOption): any {
    return option.value;
  }

  onFocus(): void {
    this.isFocused = true;
    
    // Clear any pending focus out timer
    if (this.focusOutTimer) {
      clearTimeout(this.focusOutTimer);
      this.focusOutTimer = undefined;
    }
  }

  onBlur(): void {
    this.isFocused = false;
    this.blurEvent.emit();
    
    // Add a fallback mechanism for production environments
    // Check if dropdown should close after a small delay
    this.focusOutTimer = window.setTimeout(() => {
      if (this.isOpen && !this.elementRef.nativeElement.contains(document.activeElement)) {
        console.log('Closing dropdown due to focus lost');
        this.ngZone.run(() => {
          this.closeDropdown();
          this.cdr.detectChanges();
        });
      }
    }, 150);
  }

  focusSearchInput(): void {
    setTimeout(() => {
      if (this.searchInputRef) {
        this.searchInputRef.nativeElement.focus();
      }
    });
  }
}