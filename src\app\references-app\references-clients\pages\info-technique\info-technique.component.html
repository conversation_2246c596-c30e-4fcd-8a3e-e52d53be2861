<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">Client Info Technique</h4>
      <div class="d-flex flex-wrap justify-content-end gap-2">
         <button type="button" class="btn btn-dark" title="Filtrer" (click)="toggleFilterDrawer()">
            <i class="mdi mdi-filter"></i> Filtrer
          </button>
      </div>
    </div>
  </div>
</div>





<kendo-grid [data]="infoTechniques"
style="height: calc(100vh - 130px);border-radius: 10px;"
class="winClient-stats-grid ref-grid custom-sort-grid"
 [pageable]="true" [pageSize]="navigation.pageSize" [skip]="navigation.skip"
 [sortable]="true"
 [sort]="sort"
 [rowClass]="rowClass"
 (sortChange)="sortChange($event)"
>

  <kendo-grid-column field="cliCode" title="Code Client" [width]="120">
  </kendo-grid-column>

  <kendo-grid-column field="raisonSociale" title="Raison Sociale" [width]="200">
  </kendo-grid-column>

  <kendo-grid-column field="nomPharmacien" title="Nom Pharmacien" [width]="180">
  </kendo-grid-column>

  <kendo-grid-column field="transcoWinplus" title="Transco Winplus" [width]="130">
    <ng-template kendoGridCellTemplate let-dataItem>
      <span class="badge" [ngClass]="dataItem.transcoWinplus ? 'bg-success' : 'bg-secondary'">
        {{ dataItem.transcoWinplus ? 'Oui' : 'Non' }}
      </span>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column field="typeInternet" title="Type Internet" [width]="120">
    <ng-template kendoGridCellTemplate let-dataItem>
      <span class="badge"
            [ngClass]="{
              'bg-primary': dataItem.typeInternet === 'FIBRE',
              'bg-warning': dataItem.typeInternet === 'ADSL',
              'bg-secondary': dataItem.typeInternet === 'SANS'
            }">
        {{ dataItem.typeInternet }}
      </span>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column field="versionOs" title="Version OS" [width]="120">
    <ng-template kendoGridCellTemplate let-dataItem>
      <span *ngIf="dataItem.versionOs">
        {{ getOsVersionLabel(dataItem.versionOs) }}
      </span>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column field="versionWinpharm" title="Version Winpharm" [width]="140">
    <ng-template kendoGridCellTemplate let-dataItem>
      <span *ngIf="dataItem.versionWinpharm">
        {{ getWinpharmVersionLabel(dataItem.versionWinpharm) }}
      </span>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column field="dateMaj" title="Date MAJ" [width]="90">
    <ng-template kendoGridCellTemplate let-dataItem>
      {{ dataItem.dateMaj | date:'dd/MM/yyyy' }}
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column field="gsmResponsable" title="GSM Responsable" [width]="140">
  </kendo-grid-column>

  <kendo-grid-column title="Actions" [width]="100" [sortable]="false">
    <ng-template kendoGridCellTemplate let-dataItem>
      <app-action-icon [icon]="'pencil'"  [extendClass]="'circle-lg'" (click)="openEditDrawer(dataItem)"></app-action-icon>
    </ng-template>
  </kendo-grid-column>

<ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
let-total="total">
  <wph-grid-custom-pager [totalElements]="infoTechniques.total" [totalPages]="totalPages" [currentPage]="currentPage"  [allowPageSizes]="false"
  [navigation]="navigation" style="width: 100%;"
  (pageChange)="pageChange($event)"></wph-grid-custom-pager>
</ng-template>
</kendo-grid>

<!-- Filter Drawer -->
<app-custom-drawer
  [isOpen]="isFilterDrawerOpen"
  [width]="'600px'"
  [title]="'Filtrer Info Technique'"
  (isOpenChange)="isFilterDrawerOpen = $event">

  <div class="p-2">
    <form [formGroup]="filterForm" id="filterForm" (ngSubmit)="onFilterSubmit()" appFocusTrap>
      <div class="flex-grow-1">
        <div class="row">
          <div class="col-12">
            <div class="mb-3">
              <label for="cliCode" class="form-label">Code Client</label>
              <input  appAutoFocus id="cliCode" formControlName="cliCode" type="search" class="form-control" placeholder="Tapez le code client">
            </div>
          </div>
          <ng-container *jhiHasAnyAuthority="['ROLE_SUPERADMIN']">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="nomPharmacien" class="form-label">Nom Pharmacien</label>
                <input id="nomPharmacien" formControlName="nomPharmacien" type="search" class="form-control" placeholder="Tapez nom Pharmacien">
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="raisonSociale" class="form-label">Raison Sociale</label>
                <input id="raisonSociale" formControlName="raisonSociale" type="search" class="form-control" placeholder="Tapez raison sociale">
              </div>
            </div>
            <div class="col-12 col-sm-6">
              <div class="mb-3">
                <label for="transcoWinplus" class="form-label">Transco Winplus</label>
                <select id="transcoWinplus" formControlName="transcoWinplus" class="form-select" [compareWith]="compareFn">
                  <option [ngValue]="null">Sélectionner...</option>
                  <option [ngValue]="true">Oui</option>
                  <option [ngValue]="false">Non</option>
                </select>
              </div>
            </div>
            <div class="col-12 col-sm-6">
              <div class="mb-3">
                <label for="typeInternet" class="form-label">Type Internet</label>
                <select id="typeInternet" formControlName="typeInternet" class="form-select" [compareWith]="compareFn">
                  <option [ngValue]="null">Sélectionner...</option>
                  <option *ngFor="let option of typeInternetOptions" [ngValue]="option.value">{{ option.label }}</option>
                </select>
              </div>
            </div>
            <div class="col-12 col-sm-6">
              <div class="mb-3">
                <label for="versionOs" class="form-label">Version OS</label>
                <select id="versionOs" formControlName="versionOs" class="form-select" [compareWith]="compareFn">
                  <option [ngValue]="null">Sélectionner Version OS</option>
                  <option *ngFor="let option of osVersionOptions" [ngValue]="option.value">{{ option.label }}</option>
                </select>
              </div>
            </div>
  
            <div class="col-12 col-sm-6">
              <div class="mb-3">
                <label for="versionWinpharm" class="form-label">Version Winpharm</label>
                <select id="versionWinpharm" formControlName="versionWinpharm" class="form-select" [compareWith]="compareFn">
                  <option [ngValue]="null">Sélectionner Version</option>
                  <option *ngFor="let option of winpharmVersionOptions" [ngValue]="option.value">{{ option.label }}</option>
                </select>
              </div>
            </div>
          </ng-container>
          </div>

      </div>
    </form>
  </div>

  <div drawer-footer>
    <div class="modal-footer justify-content-start">
      <button  form="filterForm" class="btn btn-primary">
        Rechercher
      </button>
      <button type="button" class="btn btn-dark" (click)="resetFilters()">
        Réinitialiser
      </button>
    </div>
  </div>
</app-custom-drawer>

<!-- Edit Modal -->
<ng-template #editModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">Modifier Info Technique</h4>
    <button type="button" class="cross-button" (click)="closeModal()">
      <i class="mdi mdi-close"></i>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="editForm" id="editForm" (ngSubmit)="onEditSubmit()" appFocusTrap>
      <div class="flex-grow-1">
        <div class="row">
          <div class="col-12 col-sm-6">
            <div class="mb-3">
              <label for="editVersionWinpharm" class="form-label">Version Winpharm</label>
              <select id="editVersionWinpharm" formControlName="versionWinpharm" class="form-select" [compareWith]="compareFn">
                <option [ngValue]="null">Sélectionner Version</option>
                <option *ngFor="let option of winpharmVersionOptions" [ngValue]="option.value">{{ option.label }}</option>
              </select>
            </div>
          </div>
          <div class="col-12 col-sm-6">
            <div class="mb-3">
              <label for="editTypeInternet" class="form-label">Type Internet</label>
              <select id="editTypeInternet" formControlName="typeInternet" class="form-select" [compareWith]="compareFn">
                <option [ngValue]="null">Sélectionner Type Internet</option>
                <option *ngFor="let option of typeInternetOptions" [ngValue]="option.value">{{ option.label }}</option>
              </select>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-12 col-sm-6">
            <div class="mb-3">
              <label for="editVersionOs" class="form-label">Version OS</label>
              <select id="editVersionOs" formControlName="versionOs" class="form-select" [compareWith]="compareFn">
                <option [ngValue]="null">Sélectionner Version OS</option>
                <option *ngFor="let option of osVersionOptions" [ngValue]="option.value">{{ option.label }}</option>
              </select>
            </div>
          </div>
          <div class="col-12 col-sm-6">
            <div class="mb-3">
              <label for="editTranscoWinplus" class="form-label">Transco Winplus</label>
              <select id="editTranscoWinplus" formControlName="transcoWinplus" class="form-select" [compareWith]="compareFn">
                <option [ngValue]="null">Sélectionner...</option>
                <option [ngValue]="true">Oui</option>
                <option [ngValue]="false">Non</option>
              </select>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-12 col-sm-6">
            <div class="mb-3">
              <label for="editGsmResponsable" class="form-label">GSM Responsable</label>
              <input id="editGsmResponsable" formControlName="gsmResponsable" class="form-control"
                     placeholder="Tapez le GSM du responsable">
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button form="editForm" class="btn btn-primary">
      <i class="mdi mdi-content-save"></i> Modifier
    </button>
    <button type="button" class="btn btn-secondary" (click)="closeModal()">
      <i class="mdi mdi-close"></i> Annuler
    </button>
  </div>
</ng-template>