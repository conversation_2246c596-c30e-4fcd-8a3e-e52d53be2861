import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { InfoTechniqueCriteria, TechniqueInfo } from '../models/infoTechnique.model';
import { Pagination } from '../../referential/models/pagination.interface';
import { Page } from '../../referential/models/Page/page.model';

@Injectable({
  providedIn: 'root'
})
export class WinclientInfoTechniqueService {

constructor(
  private http: HttpClient
) { }




createOrUpdateInfoTechnique(infoTechnique: TechniqueInfo) {
  return this.http.post<any>(`${env.winclient_base_url}/api/winclient/client-info-technique/update-info`, infoTechnique);
}


searchInfoTechnique(pagination:Pagination,criteria: Partial<InfoTechniqueCriteria>={}) {
  return this.http.post<Page<TechniqueInfo>>(`${env.winclient_base_url}/api/winclient/client-info-technique/search`, criteria,{
    params: {
      page: Math.ceil(pagination.skip / pagination.pageSize),
      size: pagination.pageSize,
      sort: pagination?.sortField ? `${pagination.sortField},${pagination.sortMethod || 'asc'}` : ''
    }
  });
}

}
